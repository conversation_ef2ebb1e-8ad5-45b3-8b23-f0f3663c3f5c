'use client';

import { Amplify } from 'aws-amplify';
import {
  signIn as amplifySignIn,
  signUp as amplifySignUp,
  signOut as amplifySignOut,
  confirmSignUp as amplifyConfirmSignUp,
  resetPassword,
  confirmResetPassword,
  getCurrentUser,
} from 'aws-amplify/auth';
import { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

interface AuthUser {
  username: string;
  email: string;
  attributes: {
    email: string;
    email_verified: boolean;
    sub: string;
    [key: string]: any;
  };
}

export const useAuth = () => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  const checkUser = useCallback(async () => {
    try {
      const userData = await getCurrentUser();
      setUser(userData as unknown as AuthUser);
    } catch (error) {
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    checkUser();
  }, [checkUser]);

  const signIn = async (email: string, password: string) => {
    try {
      const userData = await amplifySignIn({ username: email, password });
      setUser(userData as unknown as AuthUser);
      router.push('/');
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      await amplifySignUp({
        username: email,
        password,
        options: {
          userAttributes: { email },
        },
      });
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  };

  const confirmSignUp = async (email: string, code: string) => {
    try {
      await amplifyConfirmSignUp({
        username: email,
        confirmationCode: code,
      });
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  };

  const signOut = async () => {
    try {
      await amplifySignOut();
      setUser(null);
      router.push('/auth/signin');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const forgotPassword = async (email: string) => {
    try {
      await resetPassword({ username: email });
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  };

  const forgotPasswordSubmit = async (email: string, code: string, newPassword: string) => {
    try {
      await confirmResetPassword({
        username: email,
        confirmationCode: code,
        newPassword,
      });
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  };

  return {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    confirmSignUp,
    forgotPassword,
    forgotPasswordSubmit,
  };
};
