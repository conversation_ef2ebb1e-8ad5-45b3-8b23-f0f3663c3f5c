// React Query hooks for all API endpoints in api-utils.ts. Provides data fetching and mutation hooks for use in components.
import { useQuery, useMutation } from '@tanstack/react-query';
import { fetchAllDevices, fetchDeviceData, updateDeviceData } from '@/api-calls/devices';
import { fetchJobsForDevice } from '@/api-calls/jobs';
import { fetchOrgInfo } from '@/api-calls/org';
import { fetchOrgUsers, manipulateUsers } from '@/api-calls/users';
import { fetchActionLogs, submitActionLog } from '@/api-calls/logs';

// Fetch all devices
export const useAllDevices = () => useQuery({ queryKey: ['devices'], queryFn: fetchAllDevices });
// Fetch a single device by ID
export const useDeviceData = (deviceId: string) =>
  useQuery({
    queryKey: ['device', deviceId],
    queryFn: () => fetchDeviceData(deviceId),
    enabled: Boolean(deviceId),
  });
// Fetch jobs for a device/provider
export const useJobsForDevice = (
  provider: string,
  device: string,
  page?: number,
  resultsPerPage?: number,
) =>
  useQuery({
    queryKey: ['jobs', provider, device, page, resultsPerPage],
    queryFn: () => fetchJobsForDevice(provider, device, page ?? 0, resultsPerPage ?? 10),
    enabled: Boolean(provider && device),
  });
// Update device data (mutation)
export const useUpdateDeviceData = () =>
  useMutation({
    mutationFn: ({ deviceId, postBody }: { deviceId: string; postBody: any }) =>
      updateDeviceData(deviceId, postBody),
  });
// Fetch organization info
export const useOrgInfo = (orgID: string) =>
  useQuery({
    queryKey: ['org', orgID],
    queryFn: () => fetchOrgInfo(orgID),
    enabled: Boolean(orgID),
  });
// Fetch users in an organization
export const useOrgUsers = (
  orgID: string,
  page: number,
  pageSize: number,
  formatLastActive: (date: string) => string,
) =>
  useQuery({
    queryKey: ['orgUsers', orgID, page, pageSize],
    queryFn: () => fetchOrgUsers(orgID, page, pageSize, formatLastActive),
    enabled: Boolean(orgID),
  });
// Add, change, or remove users (mutation)
export const useManipulateUsers = () =>
  useMutation({
    mutationFn: ({ action, body }: { action: string; body: any }) => manipulateUsers(action, body),
  });
// Fetch action logs
export const useActionLogs = (provider: string, page: number, resultsPerPage: number) =>
  useQuery({
    queryKey: ['actionLogs', provider, page, resultsPerPage],
    queryFn: () => fetchActionLogs(provider, page, resultsPerPage),
    enabled: Boolean(provider),
  });
// Submit an action log (mutation)
export const useSubmitActionLog = () =>
  useMutation({
    mutationFn: (params: any) => submitActionLog(params),
  });
