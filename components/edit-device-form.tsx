'use client';

import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, ArrowRight, Upload, Save, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { updateDeviceData } from '@/api-calls/api-utils';
import type { DeviceData } from '@/types/device';

interface EditDeviceFormProps {
  deviceData?: DeviceData;
  isEdit?: boolean;
}

export function EditDeviceForm({ deviceData, isEdit = false }: EditDeviceFormProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('basic-info');

  const initialValues = {
    name: deviceData?.name || '',
    type: deviceData?.type || 'QPU',
    paradigm: deviceData?.paradigm || 'gate-based',
    architecture: deviceData?.architecture || 'trapped-ion',
    processorType: deviceData?.processorType || '',
    numberQubits: deviceData?.numberQubits || '',
    deviceId: deviceData?.deviceId || '',
    qbraid_id: deviceData?.qbraid_id || '',
    runPackage: deviceData?.runPackage || '',
    runInputTypes: Array.isArray(deviceData?.runInputTypes)
      ? deviceData.runInputTypes.join(', ')
      : deviceData?.runInputTypes || '',
    noiseModels: Array.isArray(deviceData?.noiseModels)
      ? deviceData.noiseModels.join(', ')
      : deviceData?.noiseModels || '',
    status: deviceData?.status || '',
    visibility: deviceData?.visibility || '',
    whiteListedDomains: Array.isArray(deviceData?.whiteListedDomains)
      ? deviceData.whiteListedDomains.join(', ')
      : deviceData?.whiteListedDomains || '',
    blackListedDomains: Array.isArray(deviceData?.blackListedDomains)
      ? deviceData.blackListedDomains.join(', ')
      : deviceData?.blackListedDomains || '',
    pricing: {
      perMinute: deviceData?.pricing?.perMinute || '',
      perTask: deviceData?.pricing?.perTask || '',
      perShot: deviceData?.pricing?.perShot || '',
    },
    deviceDescription: deviceData?.deviceDescription || '',
    deviceAboutUrl: deviceData?.deviceAboutUrl || '',
    providerDescription: deviceData?.providerDescription || '',
    deviceImage: deviceData?.deviceImage || '',
    technicalSpecifications: deviceData?.technicalSpecifications || '',
    providerLogoLight: deviceData?.providerLogoLight || '',
    providerLogoDark: deviceData?.providerLogoDark || '',
    isAvailable: deviceData?.isAvailable || '',
  };

  const [editDeviceInfo, setEditDeviceInfo] = useState<
    EditDeviceFormProps['deviceData'][] | undefined
  >(undefined);

  const validationSchema = Yup.object({
    name: Yup.string().required('Device name is required'),
    type: Yup.string().oneOf(['QPU', 'Simulator']).required('Device type is required'),
  });

  const handleCancel = () => router.back();

  const handleNextPage = (submitForm: () => void) => {
    if (activeTab === 'basic-info') setActiveTab('technical-details');
    else if (activeTab === 'technical-details') setActiveTab('access-details');
    else if (activeTab === 'access-details') setActiveTab('pricing');
    else if (activeTab === 'pricing') setActiveTab('description-images');
  };

  const handlePreviousPage = () => {
    if (activeTab === 'technical-details') setActiveTab('basic-info');
    else if (activeTab === 'access-details') setActiveTab('technical-details');
    else if (activeTab === 'pricing') setActiveTab('access-details');
    else if (activeTab === 'description-images') setActiveTab('pricing');
  };

  const getButtonText = () => {
    if (activeTab === 'description-images') {
      return 'Save Changes';
    }
    return 'Continue';
  };

  const searchParams = useSearchParams();
  const deviceId = searchParams.get('id');

  // Removed useEffect-based device data fetching in favor of the useDeviceData API hook (see parent component)

  return (
    <div className="min-h-screen bg-[#18141f] py-4 sm:py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <Card className="bg-[#262131] border-[#374151] shadow-2xl shadow-black/25">
          <CardContent className="p-0">
            <Formik
              enableReinitialize
              initialValues={
                editDeviceInfo && editDeviceInfo.length > 0 && editDeviceInfo[0]
                  ? editDeviceInfo[0]
                  : initialValues
              }
              validationSchema={validationSchema}
              onSubmit={async (values, { setSubmitting, setStatus }) => {
                const runInputTypesArray =
                  typeof values.runInputTypes === 'string'
                    ? values.runInputTypes
                        .split(',')
                        .map((s) => s.trim())
                        .filter(Boolean)
                    : Array.isArray(values.runInputTypes)
                      ? values.runInputTypes
                      : [];

                const noiseModelsArray =
                  typeof values.noiseModels === 'string'
                    ? values.noiseModels
                        .split(',')
                        .map((s) => s.trim())
                        .filter(Boolean)
                    : Array.isArray(values.noiseModels)
                      ? values.noiseModels
                      : [];
                const whiteListedDomainsArray =
                  typeof values.whiteListedDomains === 'string'
                    ? values.whiteListedDomains
                        .split(',')
                        .map((s) => s.trim())
                        .filter(Boolean)
                    : Array.isArray(values.whiteListedDomains)
                      ? values.whiteListedDomains
                      : [];

                const blackListedDomainsArray =
                  typeof values.blackListedDomains === 'string'
                    ? values.blackListedDomains
                        .split(',')
                        .map((s) => s.trim())
                        .filter(Boolean)
                    : Array.isArray(values.blackListedDomains)
                      ? values.blackListedDomains
                      : [];

                let qbraidId = values.qbraid_id;
                let postBody: string = '';

                try {
                  postBody = JSON.stringify({
                    id: values.qbraid_id,
                    fieldsToAssign: {
                      name: values.name,
                      type: values.type,
                      paradigm: values.paradigm,
                      architecture: values.type === 'QPU' ? values.architecture : undefined,
                      processorType: values.type === 'Simulator' ? values.processorType : undefined,
                      numberQubits: values.numberQubits,
                      deviceId: values.deviceId,
                      qbraidId: values.qbraid_id,
                      runPackage: values.runPackage,
                      runInputTypes: runInputTypesArray,
                      noiseModels: noiseModelsArray,
                      status: values.status,
                      visibility: values.visibility,
                      whiteListedDomains: whiteListedDomainsArray,
                      blackListedDomains: blackListedDomainsArray,
                      pricing: {
                        perMinute: values.pricing.perMinute,
                        perShot: values.pricing.perShot,
                        perTask: values.pricing.perTask,
                      },
                      deviceDescription: values.deviceDescription,
                      deviceAboutUrl: values.deviceAboutUrl,
                      providerDescription: values.providerDescription,
                      deviceImage: values.deviceImage,
                      technicalSpecifications: values.technicalSpecifications,
                      providerLogoLight: values.providerLogoLight,
                      providerLogoDark: values.providerLogoDark,
                      isAvailable: values.isAvailable,
                    },
                  });
                } catch (error) {
                  console.error('Error creating post body:', error);
                  setSubmitting(false);
                  return;
                }

                updateDeviceData(qbraidId, postBody);
                router.push('/devices');
                setSubmitting(false);
              }}
            >
              {({ values, setFieldValue, status, submitForm }) => (
                <Form>
                  {/* Header */}
                  <CardHeader className="border-b border-[#374151] bg-gradient-to-r from-[#262131] to-[#2a2136] p-6 sm:p-10">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div className="flex items-center gap-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleCancel}
                          className="text-[#94a3b8] hover:text-white hover:bg-[#374151] p-2 rounded-lg transition-all duration-200"
                        >
                          <ArrowLeft className="w-5 h-5" />
                        </Button>
                        <div className="animate-in slide-in-from-left duration-300">
                          <h1 className="text-white text-3xl sm:text-4xl font-semibold">
                            {isEdit ? 'Edit Quantum Device' : 'Add New Device'}
                          </h1>
                          <p className="text-[#94a3b8] text-base sm:text-lg mt-2 leading-relaxed">
                            {isEdit
                              ? 'Update the quantum device information on the qBraid platform.'
                              : 'Register a new quantum device to the qBraid platform.'}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-3 animate-in slide-in-from-right duration-300 delay-100">
                        <Button
                          variant="outline"
                          onClick={handleCancel}
                          className="bg-[#1f1b24] border-[#374151] text-[#94a3b8] hover:bg-[#2a2631] hover:text-white hover:border-[#4b5563] transition-all duration-200"
                        >
                          <X className="w-4 h-4 mr-2" />
                          Cancel
                        </Button>
                        <Button
                          type="button"
                          onClick={submitForm}
                          className="bg-[#8a2be2] hover:bg-[#7c2dd5] text-white shadow-lg hover:shadow-xl hover:shadow-[#8a2be2]/25 transition-all duration-200"
                        >
                          <Save className="w-4 h-4 mr-2" />
                          Save Changes
                        </Button>
                      </div>
                    </div>
                  </CardHeader>

                  <div className="p-6 sm:p-10">
                    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                      {/* Enhanced Tab Navigation */}
                      <div className="mb-10 animate-in slide-in-from-top duration-300 delay-200">
                        <TabsList className="bg-[#1f1b24] border border-[#374151] rounded-xl p-1.5 w-full h-auto grid grid-cols-2 lg:grid-cols-5 gap-1.5">
                          <TabsTrigger
                            value="basic-info"
                            className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white data-[state=active]:shadow-lg text-[#94a3b8] rounded-lg px-4 py-4 font-medium transition-all duration-300 text-sm hover:text-white hover:bg-[#8a2be2]/20"
                          >
                            Basic Info
                          </TabsTrigger>
                          <TabsTrigger
                            value="technical-details"
                            className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white data-[state=active]:shadow-lg text-[#94a3b8] rounded-lg px-4 py-4 font-medium transition-all duration-300 text-sm hover:text-white hover:bg-[#8a2be2]/20"
                          >
                            Technical
                          </TabsTrigger>
                          <TabsTrigger
                            value="access-details"
                            className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white data-[state=active]:shadow-lg text-[#94a3b8] rounded-lg px-4 py-4 font-medium transition-all duration-300 text-sm hover:text-white hover:bg-[#8a2be2]/20"
                          >
                            Access
                          </TabsTrigger>
                          <TabsTrigger
                            value="pricing"
                            className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white data-[state=active]:shadow-lg text-[#94a3b8] rounded-lg px-4 py-4 font-medium transition-all duration-300 text-sm hover:text-white hover:bg-[#8a2be2]/20"
                          >
                            Pricing
                          </TabsTrigger>
                          <TabsTrigger
                            value="description-images"
                            className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white data-[state=active]:shadow-lg text-[#94a3b8] rounded-lg px-4 py-4 font-medium transition-all duration-300 text-sm hover:text-white hover:bg-[#8a2be2]/20 col-span-2 lg:col-span-1"
                          >
                            Media
                          </TabsTrigger>
                        </TabsList>
                      </div>

                      {/* BASIC INFO TAB */}
                      <TabsContent
                        value="basic-info"
                        className="mt-0 animate-in fade-in duration-500"
                      >
                        <div className="space-y-10">
                          <div>
                            <h2 className="text-2xl font-semibold text-white mb-3">
                              Basic Information
                            </h2>
                            <p className="text-[#94a3b8] text-base mb-8 leading-relaxed max-w-3xl">
                              Essential device configuration and identification details.
                            </p>
                          </div>
                          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                            <div className="space-y-4 group">
                              <Label htmlFor="name" className="text-white font-medium text-base">
                                Device Name *
                              </Label>
                              <Field
                                as={Textarea}
                                id="name"
                                name="name"
                                placeholder="e.g. SV1"
                                className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] min-h-[50px] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563] text-base px-4 py-3"
                              />
                              <ErrorMessage
                                name="name"
                                component="div"
                                className="text-red-400 text-sm animate-in slide-in-from-top duration-200"
                              />
                              <p className="text-[#6b7280] text-sm leading-relaxed">
                                The name of the quantum device as it will appear in the platform.
                              </p>
                            </div>
                            <div className="space-y-4 group">
                              <Label htmlFor="type" className="text-white font-medium text-base">
                                Device Type *
                              </Label>
                              <Select
                                value={values.type}
                                onValueChange={(value) => setFieldValue('type', value)}
                              >
                                <SelectTrigger className="bg-[#1f1b24] border-[#374151] text-white rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563] h-[50px] px-4 text-base">
                                  <SelectValue placeholder="Select device type" />
                                </SelectTrigger>
                                <SelectContent className="bg-[#262131] border-[#374151] rounded-lg">
                                  <SelectItem
                                    value="QPU"
                                    className="text-white hover:bg-[#374151] rounded-md text-base"
                                  >
                                    QPU
                                  </SelectItem>
                                  <SelectItem
                                    value="Simulator"
                                    className="text-white hover:bg-[#374151] rounded-md text-base"
                                  >
                                    Simulator
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <p className="text-[#6b7280] text-sm leading-relaxed">
                                Whether this is a physical quantum processor or a simulator.
                              </p>
                            </div>
                            <div className="space-y-4 group">
                              <Label
                                htmlFor="paradigm"
                                className="text-white font-medium text-base"
                              >
                                Paradigm *
                              </Label>
                              <Select
                                value={values.paradigm}
                                onValueChange={(value) => setFieldValue('paradigm', value)}
                              >
                                <SelectTrigger className="bg-[#1f1b24] border-[#374151] text-white rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563] h-[50px] px-4 text-base">
                                  <SelectValue placeholder="Select paradigm" />
                                </SelectTrigger>
                                <SelectContent className="bg-[#262131] border-[#374151] rounded-lg">
                                  <SelectItem
                                    value="gate-based"
                                    className="text-white hover:bg-[#374151] rounded-md text-base"
                                  >
                                    gate-based
                                  </SelectItem>
                                  <SelectItem
                                    value="annealing"
                                    className="text-white hover:bg-[#374151] rounded-md text-base"
                                  >
                                    annealing
                                  </SelectItem>
                                  <SelectItem
                                    value="analog"
                                    className="text-white hover:bg-[#374151] rounded-md text-base"
                                  >
                                    analog
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <p className="text-[#6b7280] text-sm leading-relaxed">
                                The quantum computing paradigm used on this device.
                              </p>
                            </div>

                            {values.type === 'QPU' ? (
                              <div className="space-y-4 group">
                                <Label
                                  htmlFor="architecture"
                                  className="text-white font-medium text-base"
                                >
                                  Architecture *
                                </Label>
                                <Select
                                  value={values.architecture}
                                  onValueChange={(value) => setFieldValue('architecture', value)}
                                >
                                  <SelectTrigger className="bg-[#1f1b24] border-[#374151] text-white rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563] h-[50px] px-4 text-base">
                                    <SelectValue placeholder="Select architecture" />
                                  </SelectTrigger>
                                  <SelectContent className="bg-[#262131] border-[#374151] rounded-lg">
                                    <SelectItem
                                      value="trapped-ion"
                                      className="text-white hover:bg-[#374151] rounded-md text-base"
                                    >
                                      trapped ion
                                    </SelectItem>
                                    <SelectItem
                                      value="superconducting"
                                      className="text-white hover:bg-[#374151] rounded-md text-base"
                                    >
                                      superconducting
                                    </SelectItem>
                                    <SelectItem
                                      value="photonic"
                                      className="text-white hover:bg-[#374151] rounded-md text-base"
                                    >
                                      photonic
                                    </SelectItem>
                                    <SelectItem
                                      value="neutral-atom"
                                      className="text-white hover:bg-[#374151] rounded-md text-base"
                                    >
                                      neutral atom
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                <p className="text-[#6b7280] text-sm leading-relaxed">
                                  The physical architecture of the quantum device.
                                </p>
                              </div>
                            ) : (
                              <div className="space-y-4 group">
                                <Label
                                  htmlFor="processorType"
                                  className="text-white font-medium text-base"
                                >
                                  Processor Type *
                                </Label>
                                <Input
                                  id="processorType"
                                  name="processorType"
                                  placeholder="e.g. state vector"
                                  value={values.processorType}
                                  onChange={(e) => setFieldValue('processorType', e.target.value)}
                                  className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563] h-[50px] px-4 text-base"
                                />
                                <p className="text-[#6b7280] text-sm leading-relaxed">
                                  The processor type for this simulator (e.g. state vector).
                                </p>
                              </div>
                            )}

                            <div className="space-y-4 group">
                              <Label htmlFor="qubits" className="text-white font-medium text-base">
                                Number of Qubits *
                              </Label>
                              <Field
                                as={Input}
                                id="numberQubits"
                                name="numberQubits"
                                placeholder="e.g. 32"
                                type="number"
                                className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563] h-[50px] px-4 text-base"
                              />
                              <ErrorMessage
                                name="qubits"
                                component="div"
                                className="text-red-400 text-sm animate-in slide-in-from-top duration-200"
                              />
                              <p className="text-[#6b7280] text-sm leading-relaxed">
                                The maximum number of qubits available on this device.
                              </p>
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      {/* TECHNICAL DETAILS TAB */}
                      <TabsContent
                        value="technical-details"
                        className="mt-0 animate-in fade-in duration-500"
                      >
                        <div className="space-y-8">
                          <div>
                            <h2 className="text-xl font-semibold text-white mb-2">
                              Technical Configuration
                            </h2>
                            <p className="text-[#94a3b8] text-sm mb-6">
                              Runtime packages, input types, and noise models.
                            </p>
                          </div>
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="space-y-3 group">
                              <Label
                                htmlFor="run-package"
                                className="text-white font-medium text-sm"
                              >
                                Run Package *
                              </Label>
                              <Select
                                value={values.runPackage}
                                onValueChange={(value) => setFieldValue('runPackage', value)}
                              >
                                <SelectTrigger className="bg-[#1f1b24] border-[#374151] text-white rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]">
                                  <SelectValue placeholder="Select run package" />
                                </SelectTrigger>
                                <SelectContent className="bg-[#262131] border-[#374151] rounded-lg">
                                  <SelectItem
                                    value="braket"
                                    className="text-white hover:bg-[#374151] rounded-md"
                                  >
                                    braket
                                  </SelectItem>
                                  <SelectItem
                                    value="qiskit"
                                    className="text-white hover:bg-[#374151] rounded-md"
                                  >
                                    qiskit
                                  </SelectItem>
                                  <SelectItem
                                    value="cirq"
                                    className="text-white hover:bg-[#374151] rounded-md"
                                  >
                                    cirq
                                  </SelectItem>
                                  <SelectItem
                                    value="pennylane"
                                    className="text-white hover:bg-[#374151] rounded-md"
                                  >
                                    pennylane
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                The software package used to run jobs on this device.
                              </p>
                            </div>

                            <div className="space-y-3 group">
                              <Label
                                htmlFor="run-input-types"
                                className="text-white font-medium text-sm"
                              >
                                Run Input Types *
                              </Label>
                              <Field
                                as={Textarea}
                                id="run-input-types"
                                name="runInputTypes"
                                placeholder="e.g. braket, qasm, etc."
                                className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] min-h-[80px] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]"
                              />
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                The types of quantum circuit inputs this device accepts. Separate
                                with commas.
                              </p>
                            </div>
                          </div>

                          <div className="space-y-3 group">
                            <Label
                              htmlFor="noise-models"
                              className="text-white font-medium text-sm"
                            >
                              Noise Models
                            </Label>
                            <Field
                              as={Textarea}
                              id="noise-models"
                              name="noiseModels"
                              placeholder="e.g. ideal, aquila, etc."
                              className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] min-h-[100px] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]"
                            />
                            <p className="text-[#6b7280] text-xs leading-relaxed">
                              A list of noise models, if used. Separate with commas.
                            </p>
                          </div>
                        </div>
                      </TabsContent>

                      {/* ACCESS DETAILS TAB */}
                      <TabsContent
                        value="access-details"
                        className="mt-0 animate-in fade-in duration-500"
                      >
                        <div className="space-y-8">
                          <div>
                            <h2 className="text-xl font-semibold text-white mb-2">
                              Access Configuration
                            </h2>
                            <p className="text-[#94a3b8] text-sm mb-6">
                              Control access to the device.
                            </p>
                          </div>
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="space-y-3 group">
                              <Label
                                htmlFor="whitelisted-domains"
                                className="text-white font-medium text-sm"
                              >
                                Whitelisted Domains
                              </Label>
                              <Field
                                as={Textarea}
                                id="whitelisted-domains"
                                name="whiteListedDomains"
                                placeholder="e.g. quantumscam.net"
                                className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] min-h-[100px] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]"
                              />
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                A list of domains to grant groups of users access to this device.
                                Separate with commas.
                              </p>
                            </div>

                            <div className="space-y-3 group">
                              <Label
                                htmlFor="blacklisted-domains"
                                className="text-white font-medium text-sm"
                              >
                                Blacklisted Domains
                              </Label>
                              <Field
                                as={Textarea}
                                id="blacklisted-domains"
                                name="blackListedDomains"
                                placeholder="e.g. qnon.io"
                                className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] min-h-[100px] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]"
                              />
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                A list of domains to prohibit groups of users from accessing this
                                device. Separate with commas.
                              </p>
                            </div>
                          </div>

                          <div className="space-y-3 group">
                            <Label
                              htmlFor="device-status"
                              className="text-white font-medium text-sm"
                            >
                              Device Status *
                            </Label>
                            <Select
                              value={values.status}
                              onValueChange={(value) => setFieldValue('status', value)}
                            >
                              <SelectTrigger className="bg-[#1f1b24] border-[#374151] text-white rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]">
                                <SelectValue placeholder="Select device status" />
                              </SelectTrigger>
                              <SelectContent className="bg-[#262131] border-[#374151] rounded-lg">
                                <SelectItem
                                  value="ONLINE"
                                  className="text-white hover:bg-[#374151] rounded-md"
                                >
                                  ONLINE
                                </SelectItem>
                                <SelectItem
                                  value="OFFLINE"
                                  className="text-white hover:bg-[#374151] rounded-md"
                                >
                                  OFFLINE
                                </SelectItem>
                                <SelectItem
                                  value="RETIRED"
                                  className="text-white hover:bg-[#374151] rounded-md"
                                >
                                  RETIRED
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <p className="text-[#6b7280] text-xs leading-relaxed">
                              How the status of the device should display to users.
                            </p>
                          </div>

                          <div className="space-y-3 group">
                            <Label
                              htmlFor="device-visibility"
                              className="text-white font-medium text-sm"
                            >
                              Device Visibility *
                            </Label>
                            <Select
                              value={values.visibility}
                              onValueChange={(value) => setFieldValue('visibility', value)}
                            >
                              <SelectTrigger className="bg-[#1f1b24] border-[#374151] text-white rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]">
                                <SelectValue placeholder="Select visibility" />
                              </SelectTrigger>
                              <SelectContent className="bg-[#262131] border-[#374151] rounded-lg">
                                <SelectItem
                                  value="public"
                                  className="text-white hover:bg-[#374151] rounded-md"
                                >
                                  public
                                </SelectItem>
                                <SelectItem
                                  value="private"
                                  className="text-white hover:bg-[#374151] rounded-md"
                                >
                                  private
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <p className="text-[#6b7280] text-xs leading-relaxed">
                              Whether this device is visible to all qBraid users or if it is private
                              and only visible to certain users. See Whitelisted Domains.
                            </p>
                          </div>

                          <div className="space-y-3 group">
                            <Label htmlFor="isAvailable" className="text-white font-medium text-sm">
                              Device Avaliability *
                            </Label>
                            <Select
                              value={values.isAvailable}
                              onValueChange={(value) => setFieldValue('isAvailable', value)}
                            >
                              <SelectTrigger className="bg-[#1f1b24] border-[#374151] text-white rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]">
                                <SelectValue placeholder="Select device avaliability" />
                              </SelectTrigger>
                              <SelectContent className="bg-[#262131] border-[#374151] rounded-lg">
                                <SelectItem
                                  value="true"
                                  className="text-white hover:bg-[#374151] rounded-md"
                                >
                                  true
                                </SelectItem>
                                <SelectItem
                                  value="false"
                                  className="text-white hover:bg-[#374151] rounded-md"
                                >
                                  false
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <p className="text-[#6b7280] text-xs leading-relaxed">
                              Avaliability of the device to qBraid users.
                            </p>
                          </div>
                        </div>
                      </TabsContent>

                      {/* PRICING TAB */}
                      <TabsContent value="pricing" className="mt-0 animate-in fade-in duration-500">
                        <div className="space-y-8">
                          <div>
                            <h2 className="text-xl font-semibold text-white mb-2">
                              Pricing Configuration
                            </h2>
                            <p className="text-[#94a3b8] text-sm mb-6">
                              Cost of usage for the device.
                            </p>
                          </div>
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="space-y-3 group">
                              <Label
                                htmlFor="price-per-minute"
                                className="text-white font-medium text-sm"
                              >
                                Price per minute ($) *
                              </Label>
                              <Field
                                as={Input}
                                id="price-per-minute"
                                name="pricing.perMinute"
                                placeholder="0.00"
                                type="number"
                                step="0.01"
                                min="0"
                                className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]"
                              />
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                The cost per minute of usage.
                              </p>
                            </div>

                            <div className="space-y-3 group">
                              <Label
                                htmlFor="price-per-task"
                                className="text-white font-medium text-sm"
                              >
                                Price per task ($) *
                              </Label>
                              <Field
                                as={Input}
                                id="price-per-task"
                                name="pricing.perTask"
                                placeholder="0.00"
                                type="number"
                                step="0.01"
                                min="0"
                                className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]"
                              />
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                The cost per quantum task.
                              </p>
                            </div>
                          </div>

                          <div className="space-y-3 group">
                            <Label
                              htmlFor="price-per-shot"
                              className="text-white font-medium text-sm"
                            >
                              Price per shot ($) *
                            </Label>
                            <Field
                              as={Input}
                              id="price-per-shot"
                              name="pricing.perShot"
                              placeholder="0.00"
                              type="number"
                              step="0.01"
                              min="0"
                              className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]"
                            />
                            <p className="text-[#6b7280] text-xs leading-relaxed">
                              The cost per shot (circuit execution).
                            </p>
                          </div>
                        </div>
                      </TabsContent>

                      {/* DESCRIPTION & IMAGES TAB */}
                      <TabsContent
                        value="description-images"
                        className="mt-0 animate-in fade-in duration-500"
                      >
                        <div className="space-y-8">
                          <div>
                            <h2 className="text-xl font-semibold text-white mb-2">
                              Description and Images
                            </h2>
                            <p className="text-[#94a3b8] text-sm mb-6">
                              Provide additional information about the device.
                            </p>
                          </div>
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="space-y-3 group">
                              <Label
                                htmlFor="device-description"
                                className="text-white font-medium text-sm"
                              >
                                Device Description
                              </Label>
                              <Field
                                as={Textarea}
                                id="device-description"
                                name="deviceDescription"
                                placeholder="e.g. 11 qubit quantum processor"
                                className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] min-h-[100px] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]"
                              />
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                A short description of the device.
                              </p>
                            </div>

                            <div className="space-y-3 group">
                              <Label
                                htmlFor="device-about-url"
                                className="text-white font-medium text-sm"
                              >
                                Device About URL
                              </Label>
                              <Field
                                as={Input}
                                id="device-about-url"
                                name="deviceAboutUrl"
                                placeholder="e.g. https://example.com/quantum-systems/harmony"
                                className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]"
                              />
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                A URL with more information about the device.
                              </p>
                            </div>

                            <div className="space-y-3 group">
                              <Label
                                htmlFor="provider-description"
                                className="text-white font-medium text-sm"
                              >
                                Provider Description
                              </Label>
                              <Field
                                as={Textarea}
                                id="provider-description"
                                name="providerDescription"
                                placeholder="e.g. IonQ provides trapped ion quantum computers ..."
                                className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] min-h-[100px] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563]"
                              />
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                A description of the provider of this device.
                              </p>
                            </div>

                            <div className="space-y-3 group">
                              <Label
                                htmlFor="device-image"
                                className="text-white font-medium text-sm"
                              >
                                Device Image
                              </Label>
                              <div className="flex space-x-2">
                                <Input
                                  id="device-image"
                                  name="deviceImage"
                                  placeholder="e.g. https://example.com/device-image.jpg"
                                  className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563] flex-1"
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  className="bg-transparent border-[#374151] text-[#94a3b8] hover:bg-[#4b5563] hover:text-white px-3"
                                >
                                  <Upload className="w-4 h-4" />
                                </Button>
                              </div>
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                An image of the hardware device. Paste a URL or upload an image
                                file.
                              </p>
                            </div>

                            <div className="space-y-3 group">
                              <Label
                                htmlFor="technical-specifications"
                                className="text-white font-medium text-sm"
                              >
                                Technical Specifications
                              </Label>
                              <div className="flex space-x-2">
                                <Field
                                  as={Input}
                                  id="technical-specifications"
                                  name="technicalSpecifications"
                                  placeholder="e.g. https://example.com/quantum-systems/tech-specs.json"
                                  className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563] flex-1"
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  className="bg-transparent border-[#374151] text-[#94a3b8] hover:bg-[#4b5563] hover:text-white px-3"
                                >
                                  <Upload className="w-4 h-4" />
                                </Button>
                              </div>
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                A JSON file containing technical specifications for this device
                                (e.g. T1, T2, CLOPS). Paste a URL or upload a file.
                              </p>
                            </div>

                            <div className="space-y-3 group">
                              <Label
                                htmlFor="provider-logo-light"
                                className="text-white font-medium text-sm"
                              >
                                Provider Logo (Light Theme)
                              </Label>
                              <div className="flex space-x-2">
                                <Field
                                  as={Input}
                                  id="provider-logo-light"
                                  name="providerLogoLight"
                                  placeholder="e.g. https://example.com/device-image.jpg"
                                  className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563] flex-1"
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  className="bg-transparent border-[#374151] text-[#94a3b8] hover:bg-[#4b5563] hover:text-white px-3"
                                >
                                  <Upload className="w-4 h-4" />
                                </Button>
                              </div>
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                An image of the provider's logo for light theme. Paste a URL or
                                upload an image file.
                              </p>
                            </div>

                            <div className="space-y-3 group">
                              <Label
                                htmlFor="provider-logo-dark"
                                className="text-white font-medium text-sm"
                              >
                                Provider Logo (Dark Theme)
                              </Label>
                              <div className="flex space-x-2">
                                <Field
                                  as={Input}
                                  id="provider-logo-dark"
                                  name="providerLogoDark"
                                  placeholder="e.g. https://example.com/device-image.jpg"
                                  className="bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200 group-hover:border-[#4b5563] flex-1"
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  className="bg-transparent border-[#374151] text-[#94a3b8] hover:bg-[#4b5563] hover:text-white px-3"
                                >
                                  <Upload className="w-4 h-4" />
                                </Button>
                              </div>
                              <p className="text-[#6b7280] text-xs leading-relaxed">
                                An image of the provider's logo for dark theme. Paste a URL or
                                upload an image file.
                              </p>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>

                    {/* Enhanced Navigation Footer */}
                    <div className="flex flex-col sm:flex-row justify-between items-center pt-10 border-t border-[#374151] mt-16 gap-6 animate-in slide-in-from-bottom duration-300 delay-300">
                      <div className="flex gap-3">
                        {activeTab !== 'basic-info' ? (
                          <Button
                            variant="outline"
                            type="button"
                            onClick={handlePreviousPage}
                            className="bg-[#1f1b24] border-[#374151] text-[#94a3b8] hover:bg-[#2a2631] hover:text-white hover:border-[#4b5563] transition-all duration-200 px-6 py-3"
                          >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Previous
                          </Button>
                        ) : (
                          <div></div>
                        )}
                      </div>

                      <div className="flex items-center gap-4">
                        {status?.success && (
                          <div className="text-green-500 text-base animate-in slide-in-from-right duration-200">
                            {status.success}
                          </div>
                        )}
                        {status?.error && (
                          <div className="text-red-500 text-base animate-in slide-in-from-right duration-200">
                            {status.error}
                          </div>
                        )}
                        <Button
                          type="button"
                          onClick={() => {
                            if (activeTab === 'description-images') {
                              submitForm();
                            } else {
                              handleNextPage(() => {});
                            }
                          }}
                          className="bg-[#8a2be2] hover:bg-[#7c2dd5] text-white shadow-lg hover:shadow-xl hover:shadow-[#8a2be2]/25 transition-all duration-200 px-8 py-3 text-base"
                        >
                          {getButtonText()}
                          {activeTab !== 'description-images' && (
                            <ArrowRight className="w-4 h-4 ml-2" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
