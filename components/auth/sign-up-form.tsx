'use client';

import { useActionState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Link from 'next/link';
import { registerUser } from '@/app/auth/actions';
import { useCSRFToken, CSRFTokenInput } from '../../lib/csrf';

const initialState = {
  success: false,
  error: undefined,
  nextStep: undefined,
  email: undefined,
};

export function SignUpForm() {
  const [state, formAction] = useActionState(registerUser, initialState);
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();

  useEffect(() => {
    if (state.success && state.nextStep === 'verify' && state.email) {
      // Pass the email as a URL parameter to the verification page
      const verifyUrl = `/auth/verify?email=${encodeURIComponent(state.email)}`;
      router.push(verifyUrl);
    }
  }, [state.success, state.nextStep, state.email, router]);

  return (
    <div className="grid gap-6">
      <form action={formAction} className="space-y-4">
        {/* CSRF Token */}
        <CSRFTokenInput csrfToken={csrfToken} />

        {/* Show CSRF error if any */}
        {csrfError && (
          <Alert variant="destructive">
            <AlertDescription>
              Security token error: {csrfError}. Please refresh the page.
            </AlertDescription>
          </Alert>
        )}

        {state.error && (
          <Alert variant={state.success ? 'default' : 'destructive'}>
            <AlertDescription>{state.error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="name">Full Name</Label>
          <Input id="name" name="name" type="text" placeholder="John Doe" required />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input id="email" name="email" type="email" placeholder="<EMAIL>" required />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            name="password"
            type="password"
            placeholder="Minimum 8 characters"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <Input
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            placeholder="Confirm your password"
            required
          />
        </div>

        <Button type="submit" className="w-full" disabled={csrfLoading || !csrfToken}>
          {csrfLoading ? 'Loading...' : 'Create account'}
        </Button>
      </form>

      <div className="text-center text-sm">
        <span>
          Already have an account?{' '}
          <Link href="/auth/signin" className="text-primary hover:underline">
            Sign in
          </Link>
        </span>
      </div>
    </div>
  );
}
