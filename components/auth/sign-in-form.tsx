'use client';

import { useActionState, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Link from 'next/link';
import { authenticateUser } from '@/app/auth/actions';
import { useCSRFToken, CSRFTokenInput } from '../../lib/csrf';

const initialState = {
  success: false,
  error: undefined,
  requiresVerification: false,
  redirectTo: undefined,
};

export function SignInForm() {
  const [state, formAction] = useActionState(authenticateUser, initialState);
  const [email, setEmail] = useState('');
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirect = searchParams.get('redirect') || '/';

  useEffect(() => {
    console.log('Sign-in form state updated:', state);
    if (state.success) {
      console.log('Authentication successful, redirecting to:', state.redirectTo || redirect);
      const redirectPath = state.redirectTo || redirect;
      router.push(redirectPath);
      router.refresh();
    }
  }, [state.success, state.redirectTo, router, redirect]);

  return (
    <div className="grid gap-6">
      <form action={formAction} className="space-y-4">
        {/* CSRF Token */}
        <CSRFTokenInput csrfToken={csrfToken} />

        {/* Show CSRF error if any */}
        {csrfError && (
          <Alert variant="destructive">
            <AlertDescription>
              Security token error: {csrfError}. Please refresh the page.
            </AlertDescription>
          </Alert>
        )}

        {state.error && (
          <Alert variant={state.requiresVerification ? 'default' : 'destructive'}>
            <AlertDescription>
              {state.error}
              {state.requiresVerification && (
                <>
                  <br />
                  <Link
                    href={`/auth/verify${email ? `?email=${encodeURIComponent(email)}` : ''}`}
                    className="underline"
                  >
                    Go to verification page →
                  </Link>
                </>
              )}
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            placeholder="<EMAIL>"
            required
            onChange={(e) => setEmail(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <Input id="password" name="password" type="password" required />
        </div>

        <Button type="submit" className="w-full" disabled={csrfLoading || !csrfToken}>
          {csrfLoading ? 'Loading...' : 'Sign in'}
        </Button>
      </form>

      <div className="flex flex-col space-y-2 text-center text-sm">
        <Link href="/auth/forgot-password" className="text-primary hover:underline">
          Forgot password?
        </Link>
        <span>
          Don't have an account?{' '}
          <Link href="/auth/signup" className="text-primary hover:underline">
            Sign up
          </Link>
        </span>
      </div>
    </div>
  );
}
