#!/bin/bash

# Redis Setup Script for Development
# This script sets up Redis for local development

set -e

echo "🚀 Setting up Redis for development..."

# Detect OS
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    CYGWIN*)    MACHINE=Cygwin;;
    MINGW*)     MACHINE=MinGw;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "📋 Detected OS: ${MACHINE}"

# Function to check if Redis is already running
check_redis() {
    if redis-cli ping > /dev/null 2>&1; then
        echo "✅ Redis is already running"
        return 0
    else
        echo "❌ Redis is not running"
        return 1
    fi
}

# Function to install Redis on macOS
install_redis_mac() {
    echo "🍺 Installing Redis using Homebrew..."
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "❌ Homebrew not found. Please install Homebrew first:"
        echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
    
    # Install Redis
    brew install redis
    
    echo "✅ Redis installed successfully"
}

# Function to install Redis on Linux
install_redis_linux() {
    echo "🐧 Installing Redis on Linux..."
    
    # Detect Linux distribution
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        echo "📦 Installing Redis using apt..."
        sudo apt update
        sudo apt install -y redis-server
    elif [ -f /etc/redhat-release ]; then
        # Red Hat/CentOS/Fedora
        echo "📦 Installing Redis using yum/dnf..."
        if command -v dnf &> /dev/null; then
            sudo dnf install -y redis
        else
            sudo yum install -y redis
        fi
    else
        echo "❌ Unsupported Linux distribution"
        echo "   Please install Redis manually: https://redis.io/download"
        exit 1
    fi
    
    echo "✅ Redis installed successfully"
}

# Function to start Redis
start_redis() {
    echo "🚀 Starting Redis..."
    
    case "${MACHINE}" in
        Mac)
            # Start Redis using Homebrew services
            brew services start redis
            ;;
        Linux)
            # Start Redis using systemctl
            sudo systemctl start redis-server
            sudo systemctl enable redis-server
            ;;
        *)
            echo "❌ Unsupported OS for automatic Redis startup"
            echo "   Please start Redis manually"
            exit 1
            ;;
    esac
    
    # Wait a moment for Redis to start
    sleep 2
    
    if check_redis; then
        echo "✅ Redis started successfully"
    else
        echo "❌ Failed to start Redis"
        exit 1
    fi
}

# Function to configure Redis for development
configure_redis() {
    echo "⚙️  Configuring Redis for development..."
    
    # Test basic operations
    echo "🧪 Testing Redis operations..."
    
    # Set a test key
    redis-cli set test_key "Hello Redis" > /dev/null
    
    # Get the test key
    TEST_VALUE=$(redis-cli get test_key)
    
    if [ "$TEST_VALUE" = "Hello Redis" ]; then
        echo "✅ Redis operations working correctly"
        redis-cli del test_key > /dev/null
    else
        echo "❌ Redis operations failed"
        exit 1
    fi
    
    # Show Redis info
    echo "📊 Redis Information:"
    redis-cli info server | grep -E "redis_version|uptime_in_seconds"
    redis-cli info memory | grep -E "used_memory_human"
}

# Function to setup environment variables
setup_env() {
    echo "📝 Setting up environment variables..."
    
    ENV_FILE=".env.local"
    
    # Check if .env.local exists
    if [ ! -f "$ENV_FILE" ]; then
        echo "📄 Creating .env.local file..."
        touch "$ENV_FILE"
    fi
    
    # Check if REDIS_URL is already set
    if grep -q "REDIS_URL" "$ENV_FILE"; then
        echo "✅ REDIS_URL already configured in $ENV_FILE"
    else
        echo "➕ Adding REDIS_URL to $ENV_FILE"
        echo "" >> "$ENV_FILE"
        echo "# Redis Configuration" >> "$ENV_FILE"
        echo "REDIS_URL=redis://localhost:6379" >> "$ENV_FILE"
    fi
}

# Function to test the application integration
test_integration() {
    echo "🧪 Testing application integration..."
    
    # Check if the application is running
    if curl -s http://localhost:3000/api/health/redis > /dev/null 2>&1; then
        echo "✅ Application Redis health check passed"
        
        # Show health check result
        echo "📊 Redis Health Check Result:"
        curl -s http://localhost:3000/api/health/redis | jq '.' 2>/dev/null || curl -s http://localhost:3000/api/health/redis
    else
        echo "⚠️  Application not running or health check endpoint not available"
        echo "   Start your Next.js application and visit: http://localhost:3000/api/health/redis"
    fi
}

# Main execution
main() {
    echo "🔧 Redis Development Setup"
    echo "=========================="
    
    # Check if Redis is already running
    if check_redis; then
        echo "✅ Redis is already running, skipping installation"
    else
        # Install Redis based on OS
        case "${MACHINE}" in
            Mac)
                install_redis_mac
                ;;
            Linux)
                install_redis_linux
                ;;
            *)
                echo "❌ Unsupported OS: ${MACHINE}"
                echo "   Please install Redis manually: https://redis.io/download"
                exit 1
                ;;
        esac
        
        # Start Redis
        start_redis
    fi
    
    # Configure Redis
    configure_redis
    
    # Setup environment variables
    setup_env
    
    # Test integration
    test_integration
    
    echo ""
    echo "🎉 Redis setup complete!"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Restart your Next.js application"
    echo "   2. Test token storage: http://localhost:3000/api/store-cognito-tokens"
    echo "   3. Check Redis health: http://localhost:3000/api/health/redis"
    echo ""
    echo "🔧 Useful Redis commands:"
    echo "   redis-cli ping                    # Test connection"
    echo "   redis-cli monitor                 # Monitor commands"
    echo "   redis-cli --stat                  # Show statistics"
    echo "   redis-cli keys 'user_tokens:*'    # List user tokens"
    echo ""
}

# Run main function
main "$@"
