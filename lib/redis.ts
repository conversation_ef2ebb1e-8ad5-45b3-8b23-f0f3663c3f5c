import Redis from 'ioredis';

// Redis client singleton
let redis: Redis | null = null;

/**
 * Get Redis client instance
 * Creates a new connection if one doesn't exist
 */
export function getRedisClient(): Redis {
  if (!redis) {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

    redis = new Redis(redisUrl, {
      // Connection options
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,

      // Connection pool settings
      family: 4, // IPv4
      keepAlive: true,

      // Timeouts
      connectTimeout: 10000,
      commandTimeout: 5000,
    });

    redis.on('ready', () => {
      console.log('✅ [REDIS] Redis client ready for operations');
    });

    redis.on('error', (err: Error) => {
      console.error('❌ [REDIS] Redis connection error:', {
        message: err.message,
        name: err.name,
        timestamp: new Date().toISOString(),
      });
    });

    redis.on('close', () => {
      console.log('🔌 [REDIS] Redis connection closed');
    });

    redis.on('reconnecting', (delay: number) => {
      console.log(`🔄 [REDIS] Reconnecting to Redis in ${delay}ms...`);
    });

    redis.on('end', () => {
      console.log('🛑 [REDIS] Redis connection ended');
    });
  }

  return redis;
}

/**
 * Close Redis connection
 */
export async function closeRedisConnection(): Promise<void> {
  if (redis) {
    await redis.quit();
    redis = null;
  }
}

/**
 * Test Redis connection
 */
export async function testRedisConnection(): Promise<boolean> {
  try {
    const client = getRedisClient();
    await client.ping();
    return true;
  } catch (error) {
    console.error('Redis connection test failed:', error);
    return false;
  }
}

/**
 * Redis key helpers
 */
export const RedisKeys = {
  userTokens: (userId: string) => `user_tokens:${userId}`,
  userSession: (sessionId: string) => `session:${sessionId}`,
  revokedSession: (sessionId: string) => `revoked_session:${sessionId}`,
  rateLimitUser: (userId: string) => `rate_limit:user:${userId}`,
  rateLimitIP: (ip: string) => `rate_limit:ip:${ip}`,
  apiCache: (endpoint: string, userId: string) => `api_cache:${endpoint}:${userId}`,
} as const;

/**
 * Token storage interface
 */
export interface StoredTokens {
  refreshToken: string;
  accessToken: string;
  idToken: string;
  email: string;
  storedAt: number;
  expiresAt?: number;
}

/**
 * Redis-based token operations
 */
export class TokenStorage {
  private redis: Redis;

  constructor() {
    this.redis = getRedisClient();
  }

  /**
   * Store user tokens in Redis
   */
  async storeTokens(userId: string, tokens: Omit<StoredTokens, 'storedAt'>): Promise<void> {
    const startTime = Date.now();
    const key = RedisKeys.userTokens(userId);

    console.log(`💾 [REDIS] Storing tokens for user: ${userId}`);
    console.log(`🔑 [REDIS] Redis key: ${key}`);

    const data: StoredTokens = {
      ...tokens,
      storedAt: Date.now(),
    };

    try {
      // Store as hash for efficient partial updates
      await this.redis.hset(key, {
        refreshToken: data.refreshToken,
        accessToken: data.accessToken,
        idToken: data.idToken,
        email: data.email,
        storedAt: data.storedAt.toString(),
        expiresAt: data.expiresAt?.toString() || '',
      });

      // Set expiration (30 days)
      await this.redis.expire(key, 30 * 24 * 60 * 60);

      const duration = Date.now() - startTime;
      console.log(`✅ [REDIS] Tokens stored successfully for ${userId} in ${duration}ms`);
      console.log(`📧 [REDIS] Email: ${data.email}`);
      console.log(`⏰ [REDIS] Expiration set to 30 days`);
    } catch (error) {
      console.error(`❌ [REDIS] Failed to store tokens for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get user tokens from Redis
   */
  async getTokens(userId: string): Promise<StoredTokens | null> {
    const startTime = Date.now();
    const key = RedisKeys.userTokens(userId);

    console.log(`🔍 [REDIS] Retrieving tokens for user: ${userId}`);
    console.log(`🔑 [REDIS] Redis key: ${key}`);

    try {
      const data = await this.redis.hgetall(key);
      const duration = Date.now() - startTime;

      if (!data.refreshToken) {
        console.log(`❌ [REDIS] No tokens found for user ${userId} (${duration}ms)`);
        return null;
      }

      console.log(`✅ [REDIS] Tokens retrieved for ${userId} in ${duration}ms`);
      console.log(`📧 [REDIS] Email: ${data.email}`);
      console.log(`⏰ [REDIS] Stored at: ${new Date(parseInt(data.storedAt)).toISOString()}`);

      return {
        refreshToken: data.refreshToken,
        accessToken: data.accessToken,
        idToken: data.idToken,
        email: data.email,
        storedAt: parseInt(data.storedAt),
        expiresAt: data.expiresAt ? parseInt(data.expiresAt) : undefined,
      };
    } catch (error) {
      console.error(`❌ [REDIS] Failed to retrieve tokens for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get only refresh token for a user
   */
  async getRefreshToken(userId: string): Promise<string | null> {
    const startTime = Date.now();
    const key = RedisKeys.userTokens(userId);

    console.log(`🎫 [REDIS] Getting refresh token for user: ${userId}`);

    try {
      const refreshToken = await this.redis.hget(key, 'refreshToken');
      const duration = Date.now() - startTime;

      if (refreshToken) {
        console.log(`✅ [REDIS] Refresh token found for ${userId} in ${duration}ms`);
        console.log(`🔐 [REDIS] Token length: ${refreshToken.length} characters`);
      } else {
        console.log(`❌ [REDIS] No refresh token found for ${userId} (${duration}ms)`);
      }

      return refreshToken;
    } catch (error) {
      console.error(`❌ [REDIS] Failed to get refresh token for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update specific token fields
   */
  async updateTokens(userId: string, updates: Partial<StoredTokens>): Promise<void> {
    const key = RedisKeys.userTokens(userId);
    const updateData: Record<string, string> = {};

    if (updates.refreshToken) updateData.refreshToken = updates.refreshToken;
    if (updates.accessToken) updateData.accessToken = updates.accessToken;
    if (updates.idToken) updateData.idToken = updates.idToken;
    if (updates.expiresAt) updateData.expiresAt = updates.expiresAt.toString();

    if (Object.keys(updateData).length > 0) {
      await this.redis.hset(key, updateData);
    }
  }

  /**
   * Delete user tokens
   */
  async deleteTokens(userId: string): Promise<void> {
    const key = RedisKeys.userTokens(userId);
    await this.redis.del(key);
  }

  /**
   * Check if user has stored tokens
   */
  async hasTokens(userId: string): Promise<boolean> {
    const key = RedisKeys.userTokens(userId);
    return (await this.redis.exists(key)) === 1;
  }

  /**
   * Get all users with stored tokens (for admin/cleanup)
   */
  async getAllTokenUsers(): Promise<string[]> {
    const pattern = RedisKeys.userTokens('*');
    const keys = await this.redis.keys(pattern);
    return keys.map((key) => key.replace('user_tokens:', ''));
  }
}

// Export singleton instance
export const tokenStorage = new TokenStorage();
