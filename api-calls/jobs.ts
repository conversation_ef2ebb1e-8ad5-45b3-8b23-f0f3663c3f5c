import client from './client';
import type { JobsRowProps } from '@/types/jobs';

export interface JobsResponse {
  jobsArray: JobsRowProps[];
  total: number;
}

/**
 * Fetches quantum jobs for a specific device and provider
 */
export async function fetchJobsForDevice(
  provider: string,
  device: string,
  page = 0,
  resultsPerPage = 10,
): Promise<JobsResponse> {
  const params = new URLSearchParams();
  params.append('provider', provider);
  params.append('qbraidDeviceId', device);
  params.append('page', page.toString());
  params.append('resultsPerPage', resultsPerPage.toString());

  const res = await client.get<JobsResponse>(`/quantum-jobs/all-by-provider?${params.toString()}`);
  const data = res.data;

  if (!data.jobsArray || data.jobsArray.length === 0) {
    data.jobsArray = [{ qbraidDeviceId: 'No jobs found' } as unknown as JobsRowProps];
  }

  data.jobsArray = data.jobsArray.map((job) => {
    if (job.timeStamps?.createdAt && typeof job.timeStamps.createdAt === 'string') {
      job.timeStamps.createdAt = new Date(job.timeStamps.createdAt);
    }
    if (job.timeStamps?.endedAt && typeof job.timeStamps.endedAt === 'string') {
      job.timeStamps.endedAt = new Date(job.timeStamps.endedAt);
    }
    return job;
  });

  return data;
}
