import axios, { AxiosResponse, AxiosError } from 'axios';
import type { InternalAxiosRequestConfig } from 'axios';

// Determine base URL - now points to YOUR backend API gateway
const env = process.env.NEXT_PUBLIC_NODE_ENV;
const baseURL =
  env === 'production'
    ? process.env.NEXT_PUBLIC_GATEWAY_API_URL || 'https://your-backend.com/api'
    : env === 'staging'
      ? process.env.NEXT_PUBLIC_GATEWAY_API_URL || 'https://staging-backend.com/api'
      : process.env.NEXT_PUBLIC_GATEWAY_API_URL || 'http://localhost:8000/api';

// Create axios instance
const client = axios.create({
  baseURL,
  headers: { 'Content-Type': 'application/json' },
  withCredentials: true, // Important: Send cookies with requests
});

/**
 * Get session cookie for authentication
 * This will be sent to your backend for session validation
 */
function getSessionCookie(): string | null {
  if (typeof window === 'undefined') return null;

  const cookies = document.cookie.split(';');
  const sessionCookie = cookies.find(
    (cookie) =>
      cookie.trim().startsWith('session=') || cookie.trim().startsWith('__Secure-session='),
  );

  return sessionCookie ? sessionCookie.trim().split('=')[1] : null;
}

// Request interceptor - now only sends session info to YOUR backend
client.interceptors.request.use((config: InternalAxiosRequestConfig) => {
  config.headers = config.headers || {};

  // Add session token for your backend to validate
  const sessionToken = getSessionCookie();
  if (sessionToken) {
    config.headers['Authorization'] = `Bearer ${sessionToken}`;
  }

  // Add request ID for tracing
  config.headers['X-Request-ID'] =
    `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  // Development fallback (remove in production)
  if (process.env.NODE_ENV === 'development') {
    if (process.env.NEXT_PUBLIC_EMAIL) {
      config.headers['X-Dev-Email'] = process.env.NEXT_PUBLIC_EMAIL;
    }
    if (process.env.NEXT_PUBLIC_REFRESH_TOKEN) {
      config.headers['X-Dev-Refresh-Token'] = process.env.NEXT_PUBLIC_REFRESH_TOKEN;
    }
  }

  return config;
});

// Enhanced response interceptor with session handling
client.interceptors.response.use(
  (response: AxiosResponse) => {
    // Handle session refresh if your backend sends new tokens
    const newSessionToken = response.headers['x-new-session-token'];
    if (newSessionToken && typeof window !== 'undefined') {
      // Your backend can send a new session token if it refreshed the user's session
      document.cookie = `session=${newSessionToken}; path=/; secure; samesite=strict`;
    }

    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

    // Handle authentication errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      // Clear invalid session
      if (typeof window !== 'undefined') {
        document.cookie = 'session=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        document.cookie = '__Secure-session=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';

        // Redirect to login
        window.location.href = '/auth/signin';
      }
    }

    // Handle session refresh errors
    if (
      error.response?.status === 403 &&
      (error.response.data as any)?.code === 'SESSION_EXPIRED'
    ) {
      if (typeof window !== 'undefined') {
        // Your backend detected an expired session
        window.location.href = '/auth/signin?reason=session_expired';
      }
    }

    console.error('API Error:', {
      status: error.response?.status,
      message: error.message,
      url: error.config?.url,
      method: error.config?.method,
    });

    return Promise.reject(error);
  },
);

export default client;
