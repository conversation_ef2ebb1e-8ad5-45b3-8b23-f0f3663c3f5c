import axios, { AxiosResponse, AxiosError } from 'axios';
import type { InternalAxiosRequestConfig } from 'axios';

// Determine base URL based on environment
const env = process.env.NEXT_PUBLIC_NODE_ENV;
const baseURL =
  env === 'production'
    ? 'https://api.qbraid.com/api'
    : env === 'staging'
      ? 'https://api-staging-1.qbraid.com/api'
      : process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// Create axios instance
const client = axios.create({
  baseURL,
  headers: { 'Content-Type': 'application/json' },
});

// Request interceptor to add auth headers
client.interceptors.request.use((config: InternalAxiosRequestConfig) => {
  config.headers = config.headers || {};
  if (process.env.NEXT_PUBLIC_EMAIL) {
    config.headers['email'] = process.env.NEXT_PUBLIC_EMAIL;
  }
  if (process.env.NEXT_PUBLIC_REFRESH_TOKEN) {
    config.headers['refresh-token'] = process.env.NEXT_PUBLIC_REFRESH_TOKEN;
  }
  return config;
});

// Response interceptor for global error handling
client.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => {
    console.error('API Error:', error.response?.status, error.message);
    return Promise.reject(error);
  },
);

export default client;
