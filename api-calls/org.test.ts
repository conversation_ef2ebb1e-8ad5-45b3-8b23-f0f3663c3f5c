import client from './client';
import { fetchOrgInfo } from './org';
import { vi, describe, it, expect, afterEach } from 'vitest';

afterEach(() => vi.restoreAllMocks());

describe('org API', () => {
  it('returns org data on success', async () => {
    const payload = {
      organization: {
        org: {
          organization: {
            name: 'A',
            _id: '1',
            description: 'D',
            orgEmail: 'e',
            ownerEmail: 'o',
            logo: 'l',
            updatedAt: '2025-01-01T00:00:00Z',
          },
        },
      },
    };
    vi.spyOn(client, 'get').mockResolvedValue({ data: payload });
    const res = await fetchOrgInfo('1');
    expect(res.orgName).toBe('A');
    expect(res.orgID).toBe('1');
  });

  it('returns defaults on error', async () => {
    vi.spyOn(client, 'get').mockRejectedValue(new Error('fail'));
    const res = await fetchOrgInfo('1');
    expect(res.orgName).toBe('');
    expect(res.orgID).toBe('');
  });
});
