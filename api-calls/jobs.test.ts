import client from './client';
import { fetchJobsForDevice } from './jobs';
import { vi, describe, it, expect, afterEach } from 'vitest';

afterEach(() => vi.restoreAllMocks());

describe('jobs API', () => {
  it('returns jobs when data is present', async () => {
    const mock = { jobsArray: [{ jobId: '1', qbraidDeviceId: 'd', timeStamps: {} }], total: 1 };
    vi.spyOn(client, 'get').mockResolvedValue({ data: mock });
    const res = await fetchJobsForDevice('p', 'd');
    expect(res.jobsArray.length).toBe(1);
    expect(res.total).toBe(1);
  });

  it('falls back when no jobs found', async () => {
    vi.spyOn(client, 'get').mockResolvedValue({ data: { jobsArray: [], total: 0 } });
    const res = await fetchJobsForDevice('p', 'd');
    expect(res.jobsArray[0].qbraidDeviceId).toBe('No jobs found');
  });
});
