import client from './client';
import type { Team<PERSON>ember } from '@/types/team';

export interface OrgUsersResponse {
  users: TeamMember[];
  totalUsers: number;
}

/**
 * Fetches paginated organization users with enriched profile data
 */
export async function fetchOrgUsers(
  orgID: string,
  page: number,
  pageSize: number,
  formatLastActive: (date: string) => string,
): Promise<OrgUsersResponse> {
  try {
    const res = await client.get(`/orgs/users/${orgID}/${page}/${pageSize}`);
    const data = res.data;
    const orgUsers = data.orgUsers || [];
    const pagination = data.pagination || {};

    const enrichedUsers: TeamMember[] = await Promise.all(
      orgUsers.map(async (user: TeamMember) => {
        let profile = {} as any;
        try {
          const profileRes = await client.get(`/get-user-details?email=${user.email}`);
          profile = profileRes.data;
        } catch (err) {
          console.error('Error fetching user profile for:', user.email, err);
        }

        return {
          name:
            profile.firstName && profile.lastName
              ? `${profile.firstName} ${profile.lastName}`
              : user.email,
          email: user.email,
          firstName: profile.firstName,
          lastName: profile.lastName,
          avatar: profile.profilePhoto,
          role: user.role.charAt(0).toUpperCase() + user.role.slice(1),
          status:
            user.status === 'Active'
              ? 'Active'
              : user.status === 'Invited'
                ? 'Invited'
                : 'Deactivated',
          userCredits: Math.round(user.userCredits ?? 0),
          lastActive: user.lastActive ? formatLastActive(user.lastActive) : 'Never',
        };
      }),
    );

    return {
      users: enrichedUsers,
      totalUsers: pagination.totalUsers ?? 0,
    };
  } catch (error) {
    console.error('Error fetching organization users:', error);
    return { users: [], totalUsers: 0 };
  }
}

/**
 * Invites, updates role, or removes users
 */
export async function manipulateUsers(action: string, body: any): Promise<void> {
  let url = '';
  switch (action) {
    case 'invite':
      url = '/orgs/users/add';
      break;
    case 'changeRole':
      url = '/orgs/users/update';
      break;
    case 'remove':
      url = '/orgs/users/remove';
      break;
    default:
      throw new Error('Invalid action type for manipulateUsers().');
  }

  try {
    await client.post(url, body);
  } catch (error) {
    console.error('Error in manipulateUsers:', error);
    throw error;
  }
}
