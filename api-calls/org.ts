import client from './client';

interface OrgResponse {
  organization: {
    org: {
      organization: {
        name: string;
        _id: string;
        description: string;
        orgEmail: string;
        ownerEmail: string;
        logo: string;
        updatedAt?: string;
      };
    };
  };
}

/**
 * Fetches organization info
 */
export async function fetchOrgInfo(orgID: string) {
  try {
    const res = await client.get<OrgResponse>(`/orgs/get/${orgID}`);
    const org = res.data.organization.org.organization;
    return {
      orgName: org.name,
      orgID: org._id,
      orgDescription: org.description,
      orgEmail: org.orgEmail,
      orgOwnerEmail: org.ownerEmail,
      orgLogo: org.logo,
      orgLastUpdate: org.updatedAt ? new Date(org.updatedAt).toLocaleDateString() : 'Never',
    };
  } catch (error) {
    console.error('Error fetching organization info:', error);
    return {
      orgName: '',
      orgID: '',
      orgDescription: '',
      orgEmail: '',
      orgOwnerEmail: '',
      orgLogo: '',
      orgLastUpdate: 'Never',
    };
  }
}
