import client from './client';
import { fetchOrgUsers, manipulateUsers } from './users';
import { vi, describe, it, expect, afterEach } from 'vitest';

afterEach(() => vi.restoreAllMocks());

describe('users API', () => {
  it('fetchOrgUsers returns enriched users', async () => {
    const orgUsers = [
      {
        email: '<EMAIL>',
        role: 'user',
        status: 'Active',
        userCredits: 5,
        lastActive: '2025-01-01',
      },
    ];
    vi.spyOn(client, 'get')
      .mockResolvedValueOnce({ data: { orgUsers, pagination: { totalUsers: 1 } } })
      .mockResolvedValueOnce({ data: { firstName: 'F', lastName: 'L', profilePhoto: 'pic.png' } });
    const res = await fetchOrgUsers('1', 0, 10, () => 'X days');
    expect(client.get).toHaveBeenCalledTimes(2);
    expect(res.users[0].email).toBe('<EMAIL>');
    expect(res.totalUsers).toBe(1);
  });

  it('manipulateUsers calls correct endpoint', async () => {
    vi.spyOn(client, 'post').mockResolvedValue({ data: {} });
    await manipulateUsers('invite', { foo: 'bar' });
    expect(client.post).toHaveBeenCalledWith('/orgs/users/add', { foo: 'bar' });
  });

  it('manipulateUsers throws on invalid action', async () => {
    await expect(manipulateUsers('invalid', {})).rejects.toThrow();
  });
});
