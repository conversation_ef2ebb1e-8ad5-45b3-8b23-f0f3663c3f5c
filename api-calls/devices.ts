import client from './client';
import type { DeviceData, DeviceCardProps } from '@/types/device';

/**
 * Fetches all quantum devices
 */
export async function fetchAllDevices(): Promise<DeviceCardProps[]> {
  const res = await client.get<DeviceCardProps[]>('/quantum-devices');
  return res.data;
}

/**
 * Fetches details for a single quantum device
 */
export async function fetchDeviceData(deviceId: string): Promise<DeviceData> {
  const res = await client.get<DeviceData>(`/quantum-devices?qbraid_id=${deviceId}`);
  return res.data;
}

/**
 * Updates data for a specific device
 */
export async function updateDeviceData(deviceId: string, postBody: any): Promise<DeviceData> {
  const res = await client.put<DeviceData>(`/quantum-devices/edit?id=${deviceId}`, postBody);
  return res.data;
}
