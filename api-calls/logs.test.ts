import client from './client';
import { fetchActionLogs, submitActionLog } from './logs';
import { vi, describe, it, expect, afterEach } from 'vitest';

afterEach(() => vi.restoreAllMocks());

describe('logs API', () => {
  it('fetchActionLogs returns logs and parses dates', async () => {
    const data = { auditLogsArray: [{ action: 'X', createdAt: '2025-01-01T00:00:00Z' }] };
    vi.spyOn(client, 'get').mockResolvedValue({ data });
    const res = await fetchActionLogs('p', 0, 10);
    expect(res.auditLogsArray[0].action).toBe('X');
    expect(res.auditLogsArray[0].createdAt).toBeInstanceOf(Date);
  });

  it('submitActionLog posts correctly', async () => {
    vi.spyOn(client, 'post').mockResolvedValue({ data: {} });
    await submitActionLog({ provider: 'p', email: 'e', role: 'r', type: 't' });
    expect(client.post).toHaveBeenCalledWith('/audit-logs', {
      provider: 'p',
      email: 'e',
      role: 'r',
      type: 't',
    });
  });
});
