import client from './client';
import type { ActionLogRowProps } from '@/types/logs';

export interface ActionLogsResponse {
  auditLogsArray: ActionLogRowProps[];
}

/**
 * Fetches paginated action logs and parses timestamps
 */
export async function fetchActionLogs(
  provider: string,
  page: number,
  resultsPerPage: number,
): Promise<ActionLogsResponse> {
  try {
    const res = await client.get<ActionLogsResponse>(
      `/audit-logs/${provider}?page=${page}&resultsPerPage=${resultsPerPage}`,
    );
    const data = res.data;
    data.auditLogsArray = data.auditLogsArray.map((log) => {
      if (log.createdAt) log.createdAt = new Date(log.createdAt);
      return log;
    });
    return data;
  } catch (error) {
    console.error('Error fetching action logs:', error);
    return { auditLogsArray: [] };
  }
}

/**
 * Submits a new action log entry
 */
export async function submitActionLog(params: {
  provider: string;
  email: string;
  role: string;
  type: string;
  deviceId?: string;
  otherUserEmail?: string;
  otherUserRole?: string;
}): Promise<void> {
  try {
    await client.post('/audit-logs', params);
  } catch (error) {
    console.error('Error submitting action log:', error);
    throw error;
  }
}
