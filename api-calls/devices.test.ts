import client from './client';
import { fetchAllDevices, fetchDeviceData, updateDeviceData } from './devices';
import { vi, describe, it, expect, afterEach } from 'vitest';

afterEach(() => vi.restoreAllMocks());

describe('devices API', () => {
  it('fetchAllDevices calls correct endpoint and returns data', async () => {
    const mockData = [{ qbraid_id: '1', name: 'Device 1' }];
    vi.spyOn(client, 'get').mockResolvedValue({ data: mockData });
    const result = await fetchAllDevices();
    expect(client.get).toHaveBeenCalledWith('/quantum-devices');
    expect(result).toEqual(mockData);
  });

  it('fetchDeviceData calls correct endpoint and returns data', async () => {
    const mockData = { name: 'Device', deviceId: 'dev1' };
    vi.spyOn(client, 'get').mockResolvedValue({ data: mockData });
    const result = await fetchDeviceData('dev1');
    expect(client.get).toHaveBeenCalledWith('/quantum-devices?qbraid_id=dev1');
    expect(result).toEqual(mockData);
  });

  it('updateDeviceData calls correct endpoint and returns data', async () => {
    const mockData = { name: 'Device', deviceId: 'dev1' };
    vi.spyOn(client, 'put').mockResolvedValue({ data: mockData });
    const result = await updateDeviceData('dev1', { name: 'Device' });
    expect(client.put).toHaveBeenCalledWith('/quantum-devices/edit?id=dev1', { name: 'Device' });
    expect(result).toEqual(mockData);
  });
});
