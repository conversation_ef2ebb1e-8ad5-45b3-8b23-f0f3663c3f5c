'use client';

// Use the new API hook for device data fetching
import { useSearchParams } from 'next/navigation';
import { EditDeviceForm } from '@/components/edit-device-form';
import { useDeviceData } from '@/hooks/use-api';

export default function EditDevicePage() {
  const searchParams = useSearchParams();
  const deviceId = searchParams.get('id');

  // Fetch device data using the React Query API hook
  const { data: deviceData, isLoading } = useDeviceData(deviceId || '');

  if (isLoading || !deviceData) {
    return <div className="text-white p-8">Loading...</div>;
  }

  return <EditDeviceForm deviceData={deviceData} isEdit={true} />;
}
