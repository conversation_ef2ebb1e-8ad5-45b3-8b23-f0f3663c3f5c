import { NextRequest } from 'next/server';
import { validateSession, getAuthHeaders, createErrorResponse, getThirdPartyApiUrl } from '../_utils/auth';

/**
 * GET /api/get-user-details
 * Fetches user profile details by email
 */
export async function GET(request: NextRequest) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!email) {
      return createErrorResponse('Email parameter is required', 400);
    }

    // Build the 3rd party API URL
    const baseUrl = getThirdPartyApiUrl();
    const apiUrl = `${baseUrl}/get-user-details?email=${encodeURIComponent(email)}`;

    // Get authentication headers
    const headers = getAuthHeaders(user);

    // Make request to 3rd party API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      console.error('3rd party API error:', response.status, response.statusText);
      return createErrorResponse(
        `Failed to fetch user details: ${response.statusText}`,
        response.status
      );
    }

    const data = await response.json();

    // Return the data
    return Response.json(data);

  } catch (error) {
    console.error('Error in get-user-details GET:', error);
    return createErrorResponse('Internal server error', 500);
  }
}
