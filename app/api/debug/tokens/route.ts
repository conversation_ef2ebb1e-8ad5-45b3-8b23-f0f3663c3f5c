import { NextRequest } from 'next/server';
import { validateSession, createErrorResponse } from '../../_utils/auth';
import { tokenStorage } from '@/lib/redis';

/**
 * GET /api/debug/tokens
 * Debug endpoint to check what tokens are stored for the current user
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  console.log(`🚀 [DEBUG] GET /api/debug/tokens - Request ID: ${requestId}`);

  try {
    // Validate user session
    console.log(`🔐 [DEBUG] Validating user session...`);
    const user = await validateSession(request);
    if (!user) {
      const duration = Date.now() - startTime;
      console.log(`❌ [DEBUG] Unauthorized request (${duration}ms) - Request ID: ${requestId}`);
      return createErrorResponse('Unauthorized', 401);
    }

    console.log(`✅ [DEBUG] User authenticated: ${user.email}`);

    const userId = user.userId || user.username;
    console.log(`🔍 [DEBUG] Checking tokens for user: ${userId}`);

    // Check if user has tokens
    const hasTokens = await tokenStorage.hasTokens(userId);
    console.log(`📊 [DEBUG] User has tokens: ${hasTokens}`);

    if (!hasTokens) {
      console.log(`❌ [DEBUG] No tokens found for user ${userId}`);
      return Response.json({
        success: true,
        hasTokens: false,
        message: 'No tokens found for user',
        userId,
        requestId,
      });
    }

    // Get full token details
    const tokens = await tokenStorage.getTokens(userId);
    console.log(`📊 [DEBUG] Retrieved token details:`, {
      hasTokens: !!tokens,
      email: tokens?.email,
      refreshTokenLength: tokens?.refreshToken?.length || 0,
      accessTokenLength: tokens?.accessToken?.length || 0,
      idTokenLength: tokens?.idToken?.length || 0,
      storedAt: tokens?.storedAt ? new Date(tokens.storedAt).toISOString() : 'unknown',
    });

    // Get just the refresh token (what we use for API calls)
    const refreshToken = await tokenStorage.getRefreshToken(userId);
    console.log(`🎫 [DEBUG] Refresh token details:`, {
      hasRefreshToken: !!refreshToken,
      refreshTokenLength: refreshToken?.length || 0,
      refreshTokenPreview: refreshToken ? refreshToken.substring(0, 20) + '...' : 'none',
    });

    const totalDuration = Date.now() - startTime;
    console.log(`✅ [DEBUG] Debug request completed in ${totalDuration}ms`);

    return Response.json({
      success: true,
      hasTokens: true,
      userId,
      tokenDetails: {
        email: tokens?.email,
        refreshTokenLength: tokens?.refreshToken?.length || 0,
        accessTokenLength: tokens?.accessToken?.length || 0,
        idTokenLength: tokens?.idToken?.length || 0,
        storedAt: tokens?.storedAt ? new Date(tokens.storedAt).toISOString() : 'unknown',
        refreshTokenPreview: tokens?.refreshToken ? tokens.refreshToken.substring(0, 20) + '...' : 'none',
        accessTokenPreview: tokens?.accessToken ? tokens.accessToken.substring(0, 20) + '...' : 'none',
      },
      requestId,
      duration: totalDuration,
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ [DEBUG] Error in debug tokens (${duration}ms):`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      requestId,
      timestamp: new Date().toISOString(),
    });
    
    return createErrorResponse('Internal server error', 500);
  }
}

/**
 * DELETE /api/debug/tokens
 * Debug endpoint to clear tokens for the current user
 */
export async function DELETE(request: NextRequest) {
  const startTime = Date.now();
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  console.log(`🚀 [DEBUG] DELETE /api/debug/tokens - Request ID: ${requestId}`);

  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    const userId = user.userId || user.username;
    console.log(`🗑️ [DEBUG] Deleting tokens for user: ${userId}`);

    await tokenStorage.deleteTokens(userId);
    
    const totalDuration = Date.now() - startTime;
    console.log(`✅ [DEBUG] Tokens deleted successfully in ${totalDuration}ms`);

    return Response.json({
      success: true,
      message: 'Tokens deleted successfully',
      userId,
      requestId,
      duration: totalDuration,
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ [DEBUG] Error deleting tokens (${duration}ms):`, {
      error: error instanceof Error ? error.message : String(error),
      requestId,
    });
    
    return createErrorResponse('Internal server error', 500);
  }
}
