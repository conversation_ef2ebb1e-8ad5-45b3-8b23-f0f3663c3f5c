import { NextRequest } from 'next/server';
import { validateSession, getAuthHeaders, createErrorResponse, getThirdPartyApiUrl } from '../_utils/auth';

/**
 * GET /api/quantum-devices
 * Fetches all quantum devices or a specific device by qbraid_id
 */
export async function GET(request: NextRequest) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const qbraidId = searchParams.get('qbraid_id');

    // Build the 3rd party API URL
    const baseUrl = getThirdPartyApiUrl();
    const apiUrl = qbraidId 
      ? `${baseUrl}/quantum-devices?qbraid_id=${qbraidId}`
      : `${baseUrl}/quantum-devices`;

    // Get authentication headers
    const headers = getAuthHeaders(user);

    // Make request to 3rd party API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      console.error('3rd party API error:', response.status, response.statusText);
      return createErrorResponse(
        `Failed to fetch quantum devices: ${response.statusText}`,
        response.status
      );
    }

    const data = await response.json();

    // Return the data
    return Response.json(data);

  } catch (error) {
    console.error('Error in quantum-devices GET:', error);
    return createErrorResponse('Internal server error', 500);
  }
}
