import { NextRequest } from 'next/server';
import {
  validateSession,
  getAuthHeaders,
  createErrorResponse,
  getThirdPartyApiUrl,
} from '../_utils/auth';

/**
 * GET /api/quantum-devices
 * Fetches all quantum devices or a specific device by qbraid_id
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const ip =
    request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

  console.log(`🚀 [API] GET /api/quantum-devices - Request ID: ${requestId}`);
  console.log(`📍 [API] Client IP: ${ip}`);
  console.log(`🕐 [API] Request started at: ${new Date().toISOString()}`);

  try {
    // Validate user session
    console.log(`🔐 [API] Validating user session...`);
    const user = await validateSession(request);
    if (!user) {
      const duration = Date.now() - startTime;
      console.log(`❌ [API] Unauthorized request (${duration}ms) - Request ID: ${requestId}`);
      return createErrorResponse('Unauthorized', 401);
    }

    console.log(`✅ [API] User authenticated: ${user.email}`);

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const qbraidId = searchParams.get('qbraid_id');

    console.log(`📋 [API] Query parameters:`, {
      qbraid_id: qbraidId || 'none',
      requestType: qbraidId ? 'specific device' : 'all devices',
    });

    // Build the 3rd party API URL
    const baseUrl = getThirdPartyApiUrl();
    const apiUrl = qbraidId
      ? `${baseUrl}/quantum-devices?qbraid_id=${qbraidId}`
      : `${baseUrl}/quantum-devices`;

    console.log(`🌐 [API] Third-party API URL: ${apiUrl}`);

    // Get authentication headers
    console.log(`🔑 [API] Getting authentication headers...`);
    const headers = await getAuthHeaders(user);

    console.log(`📤 [API] Request headers:`, {
      'Content-Type': headers['Content-Type'],
      email: headers['email'] ? 'present' : 'missing',
      'refresh-token': headers['refresh-token'] ? 'present' : 'missing',
      authorization: headers['authorization'] ? 'present' : 'missing',
    });

    // Make request to 3rd party API
    console.log(`📡 [API] Making request to third-party API...`);
    const apiStartTime = Date.now();

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers,
    });

    const apiDuration = Date.now() - apiStartTime;
    console.log(`📨 [API] Third-party API response received in ${apiDuration}ms`);
    console.log(`📊 [API] Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      console.error(`❌ [API] Third-party API error:`, {
        status: response.status,
        statusText: response.statusText,
        url: apiUrl,
        requestId,
        user: user.email,
        duration: apiDuration,
      });

      return createErrorResponse(
        `Failed to fetch quantum devices: ${response.statusText}`,
        response.status,
      );
    }

    const data = await response.json();
    const totalDuration = Date.now() - startTime;

    console.log(`✅ [API] Request completed successfully in ${totalDuration}ms`);
    console.log(`📊 [API] Response data:`, {
      dataType: Array.isArray(data) ? 'array' : typeof data,
      itemCount: Array.isArray(data) ? data.length : 'N/A',
      hasDevices: data?.devices ? data.devices.length : 'N/A',
      requestId,
    });

    // Return the data
    return Response.json(data);
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ [API] Error in quantum-devices GET (${duration}ms):`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      requestId,
      timestamp: new Date().toISOString(),
    });

    return createErrorResponse('Internal server error', 500);
  }
}
