import { NextRequest } from 'next/server';
import { validateSession, getAuthHeaders, createErrorResponse, getThirdPartyApiUrl } from '../../_utils/auth';

/**
 * PUT /api/quantum-devices/edit
 * Updates data for a specific quantum device
 */
export async function PUT(request: NextRequest) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const deviceId = searchParams.get('id');

    if (!deviceId) {
      return createErrorResponse('Device ID is required', 400);
    }

    // Get request body
    const body = await request.json();

    // Build the 3rd party API URL
    const baseUrl = getThirdPartyApiUrl();
    const apiUrl = `${baseUrl}/quantum-devices/edit?id=${deviceId}`;

    // Get authentication headers
    const headers = getAuthHead<PERSON>(user);

    // Make request to 3rd party API
    const response = await fetch(apiUrl, {
      method: 'PUT',
      headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error('3rd party API error:', response.status, response.statusText);
      return createErrorResponse(
        `Failed to update quantum device: ${response.statusText}`,
        response.status
      );
    }

    const data = await response.json();

    // Log the action for audit purposes
    console.log(`Device ${deviceId} updated by user ${user.email}`);

    // Return the updated data
    return Response.json(data);

  } catch (error) {
    console.error('Error in quantum-devices PUT:', error);
    return createErrorResponse('Internal server error', 500);
  }
}
