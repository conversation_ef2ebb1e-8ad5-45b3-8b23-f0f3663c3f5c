import { NextRequest } from 'next/server';
import {
  validateSession,
  getAuthHeaders,
  createErrorResponse,
  getThirdPartyApiUrl,
} from '../../_utils/auth';

/**
 * PUT /api/quantum-devices/edit
 * Updates data for a specific quantum device
 */
export async function PUT(request: NextRequest) {
  const startTime = Date.now();
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const ip =
    request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

  console.log(`🚀 [API] PUT /api/quantum-devices/edit - Request ID: ${requestId}`);
  console.log(`📍 [API] Client IP: ${ip}`);
  console.log(`🕐 [API] Request started at: ${new Date().toISOString()}`);

  try {
    // Validate user session
    console.log(`🔐 [API] Validating user session...`);
    const user = await validateSession(request);
    if (!user) {
      const duration = Date.now() - startTime;
      console.log(`❌ [API] Unauthorized request (${duration}ms) - Request ID: ${requestId}`);
      return createErrorResponse('Unauthorized', 401);
    }

    console.log(`✅ [API] User authenticated: ${user.email}`);

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const deviceId = searchParams.get('id');

    console.log(`📋 [API] Query parameters:`, { deviceId: deviceId || 'missing' });

    if (!deviceId) {
      console.log(`❌ [API] Missing device ID - Request ID: ${requestId}`);
      return createErrorResponse('Device ID is required', 400);
    }

    // Get request body
    console.log(`📥 [API] Parsing request body...`);
    const body = await request.json();

    console.log(`📊 [API] Request body:`, {
      bodyType: typeof body,
      bodyKeys: Object.keys(body || {}),
      bodySize: JSON.stringify(body).length,
    });

    // Build the 3rd party API URL
    const baseUrl = getThirdPartyApiUrl();
    const apiUrl = `${baseUrl}/quantum-devices/edit?id=${deviceId}`;

    console.log(`🌐 [API] Third-party API URL: ${apiUrl}`);

    // Get authentication headers
    console.log(`🔑 [API] Getting authentication headers...`);
    const headers = await getAuthHeaders(user);

    console.log(`📤 [API] Request headers:`, {
      'Content-Type': headers['Content-Type'],
      email: headers['email'] ? 'present' : 'missing',
      'refresh-token': headers['refresh-token'] ? 'present' : 'missing',
      authorization: headers['authorization'] ? 'present' : 'missing',
    });

    // Make request to 3rd party API
    console.log(`📡 [API] Making PUT request to third-party API...`);
    const apiStartTime = Date.now();

    const response = await fetch(apiUrl, {
      method: 'PUT',
      headers,
      body: JSON.stringify(body),
    });

    const apiDuration = Date.now() - apiStartTime;
    console.log(`📨 [API] Third-party API response received in ${apiDuration}ms`);
    console.log(`📊 [API] Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      console.error(`❌ [API] Third-party API error:`, {
        status: response.status,
        statusText: response.statusText,
        url: apiUrl,
        deviceId,
        requestId,
        user: user.email,
        duration: apiDuration,
      });

      return createErrorResponse(
        `Failed to update quantum device: ${response.statusText}`,
        response.status,
      );
    }

    const data = await response.json();
    const totalDuration = Date.now() - startTime;

    // Log the action for audit purposes
    console.log(`✅ [API] Device update completed successfully in ${totalDuration}ms`);
    console.log(`🔧 [AUDIT] Device ${deviceId} updated by user ${user.email}`);
    console.log(`📊 [API] Response data:`, {
      dataType: typeof data,
      hasData: !!data,
      requestId,
    });

    // Return the updated data
    return Response.json(data);
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ [API] Error in quantum-devices PUT (${duration}ms):`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      requestId,
      timestamp: new Date().toISOString(),
    });

    return createErrorResponse('Internal server error', 500);
  }
}
