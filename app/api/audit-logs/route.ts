import { NextRequest } from 'next/server';
import { validateSession, getAuthHeaders, createErrorResponse, getThirdPartyApiUrl } from '../_utils/auth';

/**
 * GET /api/audit-logs/[provider]
 * Fetches paginated audit logs for a provider
 */
export async function GET(request: NextRequest) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider');
    const page = searchParams.get('page') || '0';
    const resultsPerPage = searchParams.get('resultsPerPage') || '10';

    if (!provider) {
      return createErrorResponse('Provider parameter is required', 400);
    }

    // Build the 3rd party API URL
    const baseUrl = getThirdPartyApiUrl();
    const apiUrl = `${baseUrl}/audit-logs/${provider}?page=${page}&resultsPerPage=${resultsPerPage}`;

    // Get authentication headers
    const headers = getAuthHeaders(user);

    // Make request to 3rd party API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      console.error('3rd party API error:', response.status, response.statusText);
      return createErrorResponse(
        `Failed to fetch audit logs: ${response.statusText}`,
        response.status
      );
    }

    const data = await response.json();

    // Process timestamps (same logic as in the original client)
    if (data.auditLogsArray) {
      data.auditLogsArray = data.auditLogsArray.map((log: any) => {
        if (log.createdAt) {
          log.createdAt = new Date(log.createdAt);
        }
        return log;
      });
    }

    // Return the processed data
    return Response.json(data);

  } catch (error) {
    console.error('Error in audit-logs GET:', error);
    return createErrorResponse('Internal server error', 500);
  }
}

/**
 * POST /api/audit-logs
 * Submits a new audit log entry
 */
export async function POST(request: NextRequest) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    // Get request body
    const body = await request.json();

    // Build the 3rd party API URL
    const baseUrl = getThirdPartyApiUrl();
    const apiUrl = `${baseUrl}/audit-logs`;

    // Get authentication headers
    const headers = getAuthHeaders(user);

    // Make request to 3rd party API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error('3rd party API error:', response.status, response.statusText);
      return createErrorResponse(
        `Failed to submit audit log: ${response.statusText}`,
        response.status
      );
    }

    const data = await response.json();

    // Log the action
    console.log(`Audit log submitted by ${user.email}:`, body);

    // Return the response
    return Response.json(data);

  } catch (error) {
    console.error('Error in audit-logs POST:', error);
    return createErrorResponse('Internal server error', 500);
  }
}
