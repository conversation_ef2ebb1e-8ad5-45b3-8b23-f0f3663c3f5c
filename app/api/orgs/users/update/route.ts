import { NextRequest } from 'next/server';
import { validateSession, getAuthHeaders, createErrorResponse, getThirdPartyApiUrl } from '../../../_utils/auth';

/**
 * POST /api/orgs/users/update
 * Updates a user's role in the organization
 */
export async function POST(request: NextRequest) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    // Get request body
    const body = await request.json();

    // Build the 3rd party API URL
    const baseUrl = getThirdPartyApiUrl();
    const apiUrl = `${baseUrl}/orgs/users/update`;

    // Get authentication headers
    const headers = await getAuthHeaders(user);

    // Make request to 3rd party API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error('3rd party API error:', response.status, response.statusText);
      return createErrorResponse(
        `Failed to update user role: ${response.statusText}`,
        response.status
      );
    }

    const data = await response.json();

    // Log the action for audit purposes
    console.log(`User role updated by ${user.email}:`, body);

    // Return the response
    return Response.json(data);

  } catch (error) {
    console.error('Error in orgs users update POST:', error);
    return createErrorResponse('Internal server error', 500);
  }
}
