import { NextRequest } from 'next/server';
import { validateSession, getAuthHeaders, createErrorResponse, getThirdPartyApiUrl } from '../../../_utils/auth';

/**
 * GET /api/orgs/users/[orgId]/[page]/[pageSize]
 * Fetches paginated organization users
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { params: string[] } }
) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    const [orgId, page, pageSize] = params.params;

    if (!orgId || !page || !pageSize) {
      return createErrorResponse('Organization ID, page, and pageSize are required', 400);
    }

    // Build the 3rd party API URL
    const baseUrl = getThirdPartyApiUrl();
    const apiUrl = `${baseUrl}/orgs/users/${orgId}/${page}/${pageSize}`;

    // Get authentication headers
    const headers = getAuthHeaders(user);

    // Make request to 3rd party API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      console.error('3rd party API error:', response.status, response.statusText);
      return createErrorResponse(
        `Failed to fetch organization users: ${response.statusText}`,
        response.status
      );
    }

    const data = await response.json();

    // Return the data (enrichment will be handled by the client-side function)
    return Response.json(data);

  } catch (error) {
    console.error('Error in orgs users GET:', error);
    return createErrorResponse('Internal server error', 500);
  }
}
