import { NextRequest } from 'next/server';
import { validateSession, getAuthHeaders, createErrorResponse, getThirdPartyApiUrl } from '../../../_utils/auth';

/**
 * GET /api/orgs/get/[orgId]
 * Fetches organization information
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orgId: string } }
) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    const { orgId } = params;

    if (!orgId) {
      return createErrorResponse('Organization ID is required', 400);
    }

    // Build the 3rd party API URL
    const baseUrl = getThirdPartyApiUrl();
    const apiUrl = `${baseUrl}/orgs/get/${orgId}`;

    // Get authentication headers
    const headers = await getAuthHeaders(user);

    // Make request to 3rd party API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      console.error('3rd party API error:', response.status, response.statusText);
      return createErrorResponse(
        `Failed to fetch organization info: ${response.statusText}`,
        response.status
      );
    }

    const data = await response.json();

    // Return the data
    return Response.json(data);

  } catch (error) {
    console.error('Error in orgs GET:', error);
    return createErrorResponse('Internal server error', 500);
  }
}
