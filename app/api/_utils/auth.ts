import { NextRequest } from 'next/server';
import { getSession } from '@/lib/session';

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    username: string;
    email: string;
    userId?: string;
    signedIn: boolean;
  };
}

/**
 * Validates session and extracts user information from request
 */
export async function validateSession(request: NextRequest) {
  try {
    // Get session from cookies
    const sessionData = await getSession();

    if (!sessionData || !sessionData.signedIn) {
      return null;
    }

    return {
      username: sessionData.username,
      email: sessionData.email,
      userId: sessionData.userId,
      signedIn: sessionData.signedIn,
    };
  } catch (error) {
    console.error('Session validation failed:', error);
    return null;
  }
}

/**
 * Get stored refresh token for a user
 * This retrieves tokens from Redis storage
 */
async function getStoredRefreshToken(userId: string): Promise<string | null> {
  try {
    // Import the token storage function
    const { getStoredRefreshTokenForUser } = await import('../store-cognito-tokens/route');
    return await getStoredRefreshTokenForUser(userId);
  } catch (error) {
    console.warn('Could not retrieve stored refresh token:', error);
    return null;
  }
}

/**
 * Get authentication headers for 3rd party API requests
 */
export async function getAuthHeaders(user: any): Promise<Record<string, string>> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add user email for API authentication
  if (user?.email) {
    headers['email'] = user.email;
  }

  // Try to get stored refresh token for the user
  if (user?.userId) {
    try {
      const storedRefreshToken = await getStoredRefreshToken(user.userId);
      if (storedRefreshToken) {
        headers['refresh-token'] = storedRefreshToken;
      }
    } catch (error) {
      console.warn('Could not retrieve stored refresh token:', error);
    }
  }

  // Fallback to environment variables in development
  if (process.env.NODE_ENV === 'development') {
    if (!headers['email'] && process.env.NEXT_PUBLIC_EMAIL) {
      headers['email'] = process.env.NEXT_PUBLIC_EMAIL;
    }

    if (!headers['refresh-token'] && process.env.NEXT_PUBLIC_REFRESH_TOKEN) {
      headers['refresh-token'] = process.env.NEXT_PUBLIC_REFRESH_TOKEN;
    }
  }

  return headers;
}

/**
 * Create standardized error responses
 */
export function createErrorResponse(message: string, status: number) {
  return Response.json({ error: message, timestamp: new Date().toISOString() }, { status });
}

/**
 * Get the base URL for 3rd party API based on environment
 */
export function getThirdPartyApiUrl() {
  const env = process.env.NEXT_PUBLIC_NODE_ENV;

  switch (env) {
    case 'production':
      return 'https://api.qbraid.com/api';
    case 'staging':
      return 'https://api-staging-1.qbraid.com/api';
    default:
      return process.env.THIRD_PARTY_API_URL || 'https://api.qbraid.com/api';
  }
}
