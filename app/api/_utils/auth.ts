import { NextRequest } from 'next/server';
import { getSession } from '@/lib/session';

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    username: string;
    email: string;
    userId?: string;
    signedIn: boolean;
  };
}

/**
 * Validates session and extracts user information from request
 */
export async function validateSession(request: NextRequest) {
  const startTime = Date.now();
  const requestId = request.headers.get('x-request-id') || 'unknown';
  const userAgent = request.headers.get('user-agent') || 'unknown';
  const ip =
    request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

  console.log(`🔐 [AUTH] Validating session for request: ${requestId}`);
  console.log(`📍 [AUTH] IP: ${ip}, User-Agent: ${userAgent.substring(0, 50)}...`);

  try {
    // Get session from cookies
    const sessionData = await getSession();
    const duration = Date.now() - startTime;

    if (!sessionData || !sessionData.signedIn) {
      console.log(`❌ [AUTH] Session validation failed - no valid session (${duration}ms)`);
      console.log(`🍪 [AUTH] Session data:`, sessionData ? 'exists but not signed in' : 'null');
      return null;
    }

    console.log(`✅ [AUTH] Session validated successfully in ${duration}ms`);
    console.log(
      `👤 [AUTH] User: ${sessionData.email} (${sessionData.userId || sessionData.username})`,
    );
    console.log(`🕐 [AUTH] Session issued: ${new Date(sessionData.iat * 1000).toISOString()}`);
    console.log(`⏰ [AUTH] Session expires: ${new Date(sessionData.exp * 1000).toISOString()}`);
    console.log(`📊 [AUTH] Full session data:`, {
      username: sessionData.username,
      email: sessionData.email,
      userId: sessionData.userId,
      signedIn: sessionData.signedIn,
      hasUserId: !!sessionData.userId,
      hasUsername: !!sessionData.username,
    });

    return {
      username: sessionData.username,
      email: sessionData.email,
      userId: sessionData.userId,
      signedIn: sessionData.signedIn,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ [AUTH] Session validation error (${duration}ms):`, {
      error: error instanceof Error ? error.message : String(error),
      requestId,
      ip,
      timestamp: new Date().toISOString(),
    });
    return null;
  }
}

/**
 * Get stored refresh token for a user
 * This retrieves tokens from Redis storage
 */
async function getStoredRefreshToken(userId: string): Promise<string | null> {
  try {
    // Import the token storage function
    const { getStoredRefreshTokenForUser } = await import('../store-cognito-tokens/route');
    return await getStoredRefreshTokenForUser(userId);
  } catch (error) {
    console.warn('Could not retrieve stored refresh token:', error);
    return null;
  }
}

/**
 * Get authentication headers for 3rd party API requests
 */
export async function getAuthHeaders(user: any): Promise<Record<string, string>> {
  const startTime = Date.now();
  const userId = user?.userId || user?.username || 'unknown';

  console.log(`🔑 [AUTH] Building auth headers for user: ${user?.email || 'unknown'}`);
  console.log(`👤 [AUTH] User ID: ${userId}`);

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add user email for API authentication
  if (user?.email) {
    headers['email'] = user.email;
    console.log(`📧 [AUTH] Added email header: ${user.email}`);
  } else {
    console.log(`⚠️ [AUTH] No email available for user`);
  }

  // Try to get stored refresh token for the user
  const actualUserId = user?.userId || user?.username;
  if (actualUserId) {
    console.log(
      `🎫 [AUTH] Attempting to retrieve stored refresh token for userId: ${actualUserId}...`,
    );
    try {
      const tokenStartTime = Date.now();
      const storedRefreshToken = await getStoredRefreshToken(actualUserId);
      const tokenDuration = Date.now() - tokenStartTime;

      if (storedRefreshToken) {
        headers['refresh-token'] = storedRefreshToken;
        console.log(
          `✅ [AUTH] Retrieved stored refresh token in ${tokenDuration}ms (length: ${storedRefreshToken.length})`,
        );
      } else {
        console.log(
          `❌ [AUTH] No stored refresh token found for user ${actualUserId} (${tokenDuration}ms)`,
        );
      }
    } catch (error) {
      console.error(`❌ [AUTH] Error retrieving stored refresh token:`, {
        error: error instanceof Error ? error.message : String(error),
        userId: actualUserId,
      });
    }
  } else {
    console.log(
      `⚠️ [AUTH] No user ID available (userId: ${user?.userId}, username: ${user?.username}), skipping token retrieval`,
    );
  }

  // Fallback to environment variables in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔧 [AUTH] Development mode - checking environment fallbacks...`);

    if (!headers['email'] && process.env.NEXT_PUBLIC_EMAIL) {
      headers['email'] = process.env.NEXT_PUBLIC_EMAIL;
      console.log(
        `🔧 [AUTH] Using fallback email from environment: ${process.env.NEXT_PUBLIC_EMAIL}`,
      );
    }

    if (!headers['refresh-token'] && process.env.NEXT_PUBLIC_REFRESH_TOKEN) {
      headers['refresh-token'] = process.env.NEXT_PUBLIC_REFRESH_TOKEN;
      console.log(
        `🔧 [AUTH] Using fallback refresh token from environment (length: ${process.env.NEXT_PUBLIC_REFRESH_TOKEN.length})`,
      );
    }
  }

  const duration = Date.now() - startTime;
  console.log(`✅ [AUTH] Auth headers built in ${duration}ms:`, {
    hasEmail: !!headers['email'],
    hasRefreshToken: !!headers['refresh-token'],
    hasAuthorization: !!headers['authorization'],
    headerCount: Object.keys(headers).length,
  });

  return headers;
}

/**
 * Create standardized error responses
 */
export function createErrorResponse(message: string, status: number) {
  return Response.json({ error: message, timestamp: new Date().toISOString() }, { status });
}

/**
 * Get the base URL for 3rd party API based on environment
 */
export function getThirdPartyApiUrl() {
  const env = process.env.NEXT_PUBLIC_NODE_ENV;

  switch (env) {
    case 'production':
      return 'https://api.qbraid.com/api';
    case 'staging':
      return 'https://api-staging-1.qbraid.com/api';
    default:
      return process.env.THIRD_PARTY_API_URL || 'https://api.qbraid.com/api';
  }
}
