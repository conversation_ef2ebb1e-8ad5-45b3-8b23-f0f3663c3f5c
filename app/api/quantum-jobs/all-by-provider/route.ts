import { NextRequest } from 'next/server';
import {
  validateSession,
  getAuthHeaders,
  createErrorResponse,
  getThirdPartyApiUrl,
} from '../../_utils/auth';

/**
 * GET /api/quantum-jobs/all-by-provider
 * Fetches quantum jobs for a specific device and provider
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const ip =
    request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

  console.log(`🚀 [API] GET /api/quantum-jobs/all-by-provider - Request ID: ${requestId}`);
  console.log(`📍 [API] Client IP: ${ip}`);
  console.log(`🕐 [API] Request started at: ${new Date().toISOString()}`);

  try {
    // Validate user session
    console.log(`🔐 [API] Validating user session...`);
    const user = await validateSession(request);
    if (!user) {
      const duration = Date.now() - startTime;
      console.log(`❌ [API] Unauthorized request (${duration}ms) - Request ID: ${requestId}`);
      return createErrorResponse('Unauthorized', 401);
    }

    console.log(`✅ [API] User authenticated: ${user.email}`);

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider');
    const qbraidDeviceId = searchParams.get('qbraidDeviceId');
    const page = searchParams.get('page') || '0';
    const resultsPerPage = searchParams.get('resultsPerPage') || '10';

    console.log(`📋 [API] Query parameters:`, {
      provider: provider || 'missing',
      qbraidDeviceId: qbraidDeviceId || 'missing',
      page,
      resultsPerPage,
    });

    // Validate required parameters
    if (!provider || !qbraidDeviceId) {
      console.log(`❌ [API] Missing required parameters - Request ID: ${requestId}`);
      return createErrorResponse('Provider and qbraidDeviceId are required', 400);
    }

    // Build the 3rd party API URL with query parameters
    const baseUrl = getThirdPartyApiUrl();
    const params = new URLSearchParams({
      provider,
      qbraidDeviceId,
      page,
      resultsPerPage,
    });
    const apiUrl = `${baseUrl}/quantum-jobs/all-by-provider?${params.toString()}`;

    console.log(`🌐 [API] Third-party API URL: ${apiUrl}`);

    // Get authentication headers
    console.log(`🔑 [API] Getting authentication headers...`);
    const headers = await getAuthHeaders(user);

    console.log(`📤 [API] Request headers:`, {
      'Content-Type': headers['Content-Type'],
      email: headers['email'] ? 'present' : 'missing',
      'refresh-token': headers['refresh-token'] ? 'present' : 'missing',
      authorization: headers['authorization'] ? 'present' : 'missing',
    });

    // Make request to 3rd party API
    console.log(`📡 [API] Making request to third-party API...`);
    const apiStartTime = Date.now();

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers,
    });

    const apiDuration = Date.now() - apiStartTime;
    console.log(`📨 [API] Third-party API response received in ${apiDuration}ms`);
    console.log(`📊 [API] Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      console.error(`❌ [API] Third-party API error:`, {
        status: response.status,
        statusText: response.statusText,
        url: apiUrl,
        provider,
        qbraidDeviceId,
        requestId,
        user: user.email,
        duration: apiDuration,
      });

      return createErrorResponse(
        `Failed to fetch quantum jobs: ${response.statusText}`,
        response.status,
      );
    }

    const data = await response.json();
    console.log(`📊 [API] Raw data received:`, {
      hasJobsArray: !!data.jobsArray,
      jobsCount: data.jobsArray?.length || 0,
      dataKeys: Object.keys(data || {}),
    });

    // Process the data (same logic as in the original client)
    if (!data.jobsArray || data.jobsArray.length === 0) {
      console.log(`⚠️ [API] No jobs found, setting default message`);
      data.jobsArray = [{ qbraidDeviceId: 'No jobs found' }];
    }

    // Parse timestamps
    console.log(`🕐 [API] Processing timestamps for ${data.jobsArray.length} jobs...`);
    let timestampProcessed = 0;

    data.jobsArray = data.jobsArray.map((job: any) => {
      if (job.timeStamps?.createdAt && typeof job.timeStamps.createdAt === 'string') {
        job.timeStamps.createdAt = new Date(job.timeStamps.createdAt);
        timestampProcessed++;
      }
      if (job.timeStamps?.endedAt && typeof job.timeStamps.endedAt === 'string') {
        job.timeStamps.endedAt = new Date(job.timeStamps.endedAt);
        timestampProcessed++;
      }
      return job;
    });

    const totalDuration = Date.now() - startTime;
    console.log(`✅ [API] Request completed successfully in ${totalDuration}ms`);
    console.log(`📊 [API] Final response data:`, {
      jobsCount: data.jobsArray.length,
      timestampsProcessed: timestampProcessed,
      requestId,
    });

    // Return the processed data
    return Response.json(data);
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ [API] Error in quantum-jobs GET (${duration}ms):`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      requestId,
      timestamp: new Date().toISOString(),
    });

    return createErrorResponse('Internal server error', 500);
  }
}
