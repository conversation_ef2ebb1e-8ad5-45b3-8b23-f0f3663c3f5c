import { NextRequest } from 'next/server';
import { validateSession, getAuthHeaders, createErrorResponse, getThirdPartyApiUrl } from '../../_utils/auth';

/**
 * GET /api/quantum-jobs/all-by-provider
 * Fetches quantum jobs for a specific device and provider
 */
export async function GET(request: NextRequest) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider');
    const qbraidDeviceId = searchParams.get('qbraidDeviceId');
    const page = searchParams.get('page') || '0';
    const resultsPerPage = searchParams.get('resultsPerPage') || '10';

    // Validate required parameters
    if (!provider || !qbraidDeviceId) {
      return createErrorResponse('Provider and qbraidDeviceId are required', 400);
    }

    // Build the 3rd party API URL with query parameters
    const baseUrl = getThirdPartyApiUrl();
    const params = new URLSearchParams({
      provider,
      qbraidDeviceId,
      page,
      resultsPerPage,
    });
    const apiUrl = `${baseUrl}/quantum-jobs/all-by-provider?${params.toString()}`;

    // Get authentication headers
    const headers = getAuthHeaders(user);

    // Make request to 3rd party API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      console.error('3rd party API error:', response.status, response.statusText);
      return createErrorResponse(
        `Failed to fetch quantum jobs: ${response.statusText}`,
        response.status
      );
    }

    const data = await response.json();

    // Process the data (same logic as in the original client)
    if (!data.jobsArray || data.jobsArray.length === 0) {
      data.jobsArray = [{ qbraidDeviceId: 'No jobs found' }];
    }

    // Parse timestamps
    data.jobsArray = data.jobsArray.map((job: any) => {
      if (job.timeStamps?.createdAt && typeof job.timeStamps.createdAt === 'string') {
        job.timeStamps.createdAt = new Date(job.timeStamps.createdAt);
      }
      if (job.timeStamps?.endedAt && typeof job.timeStamps.endedAt === 'string') {
        job.timeStamps.endedAt = new Date(job.timeStamps.endedAt);
      }
      return job;
    });

    // Return the processed data
    return Response.json(data);

  } catch (error) {
    console.error('Error in quantum-jobs GET:', error);
    return createErrorResponse('Internal server error', 500);
  }
}
