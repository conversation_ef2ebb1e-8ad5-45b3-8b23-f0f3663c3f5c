import { NextRequest } from 'next/server';
import { testRedisConnection, getRedisClient, RedisKeys } from '@/lib/redis';
import { createErrorResponse } from '../../_utils/auth';

/**
 * GET /api/health/redis
 * Health check endpoint for Redis connection
 */
export async function GET(request: NextRequest) {
  try {
    // Test basic Redis connection
    const isConnected = await testRedisConnection();
    
    if (!isConnected) {
      return createErrorResponse('Redis connection failed', 503);
    }

    // Get Redis client info
    const redis = getRedisClient();
    const info = await redis.info('server');
    const memory = await redis.info('memory');
    
    // Test basic operations
    const testKey = 'health_check_test';
    const testValue = Date.now().toString();
    
    await redis.set(testKey, testValue, 'EX', 10); // Expire in 10 seconds
    const retrievedValue = await redis.get(testKey);
    await redis.del(testKey);
    
    const operationsWork = retrievedValue === testValue;

    // Get some basic stats
    const dbSize = await redis.dbsize();
    const uptime = await redis.lastsave();

    return Response.json({
      status: 'healthy',
      connected: isConnected,
      operationsWork,
      stats: {
        dbSize,
        uptime: new Date(uptime * 1000).toISOString(),
      },
      info: {
        server: parseRedisInfo(info),
        memory: parseRedisInfo(memory),
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Redis health check failed:', error);
    return createErrorResponse(`Redis health check failed: ${error.message}`, 503);
  }
}

/**
 * Parse Redis INFO command output
 */
function parseRedisInfo(info: string): Record<string, string> {
  const result: Record<string, string> = {};
  const lines = info.split('\r\n');
  
  for (const line of lines) {
    if (line.includes(':') && !line.startsWith('#')) {
      const [key, value] = line.split(':');
      result[key] = value;
    }
  }
  
  return result;
}

/**
 * POST /api/health/redis
 * Advanced Redis diagnostics (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // In a real app, you'd want to check admin permissions here
    // For now, we'll just run diagnostics
    
    const redis = getRedisClient();
    
    // Test various Redis operations
    const diagnostics = {
      ping: await redis.ping(),
      time: await redis.time(),
      clientList: await redis.client('LIST'),
      configGet: await redis.config('GET', 'maxmemory'),
      keyspaceInfo: await redis.info('keyspace'),
    };

    // Test token storage operations
    const testUserId = 'health_check_user';
    const testTokens = {
      refreshToken: 'test_refresh_token',
      accessToken: 'test_access_token',
      idToken: 'test_id_token',
      email: '<EMAIL>',
    };

    // Test storing and retrieving tokens
    const tokenKey = RedisKeys.userTokens(testUserId);
    await redis.hset(tokenKey, {
      refreshToken: testTokens.refreshToken,
      accessToken: testTokens.accessToken,
      idToken: testTokens.idToken,
      email: testTokens.email,
      storedAt: Date.now().toString(),
    });

    const retrievedTokens = await redis.hgetall(tokenKey);
    await redis.del(tokenKey); // Cleanup

    const tokenOperationsWork = retrievedTokens.refreshToken === testTokens.refreshToken;

    return Response.json({
      status: 'diagnostics_complete',
      diagnostics,
      tokenOperationsWork,
      retrievedTokens: tokenOperationsWork ? 'OK' : retrievedTokens,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Redis diagnostics failed:', error);
    return createErrorResponse(`Redis diagnostics failed: ${error.message}`, 500);
  }
}
