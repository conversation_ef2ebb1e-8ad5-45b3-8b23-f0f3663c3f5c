import { NextRequest } from 'next/server';
import { validateSession, createErrorResponse } from '../_utils/auth';
import { tokenStorage, testRedisConnection } from '@/lib/redis';

/**
 * POST /api/store-cognito-tokens
 * Stores Cognito tokens for the authenticated user
 */
export async function POST(request: NextRequest) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    // Get request body
    const body = await request.json();
    const { cognitoRefreshToken, cognitoAccessToken, cognitoIdToken } = body;

    if (!cognitoRefreshToken) {
      return createErrorResponse('Cognito refresh token is required', 400);
    }

    // Store tokens for the user using Redis
    const userId = user.userId || user.username;
    await tokenStorage.storeTokens(userId, {
      refreshToken: cognitoRefreshToken,
      accessToken: cognitoAccessToken || '',
      idToken: cognitoIdToken || '',
      email: user.email,
    });

    console.log(`Stored Cognito tokens for user: ${user.email}`);

    return Response.json({
      success: true,
      message: 'Tokens stored successfully',
      userId: userId,
    });
  } catch (error) {
    console.error('Error storing Cognito tokens:', error);
    return createErrorResponse('Internal server error', 500);
  }
}

/**
 * GET /api/store-cognito-tokens
 * Retrieves stored tokens for the authenticated user (for debugging)
 */
export async function GET(request: NextRequest) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    const userId = user.userId || user.username;
    const tokens = await tokenStorage.getTokens(userId);

    if (!tokens) {
      return Response.json({
        hasTokens: false,
        message: 'No tokens found for user',
      });
    }

    return Response.json({
      hasTokens: true,
      storedAt: new Date(tokens.storedAt).toISOString(),
      hasRefreshToken: !!tokens.refreshToken,
      hasAccessToken: !!tokens.accessToken,
      hasIdToken: !!tokens.idToken,
      email: tokens.email,
    });
  } catch (error) {
    console.error('Error retrieving token info:', error);
    return createErrorResponse('Internal server error', 500);
  }
}

/**
 * Export function to get stored refresh token (used by auth utils)
 */
export async function getStoredRefreshTokenForUser(userId: string): Promise<string | null> {
  try {
    return await tokenStorage.getRefreshToken(userId);
  } catch (error) {
    console.error('Error getting refresh token:', error);
    return null;
  }
}
