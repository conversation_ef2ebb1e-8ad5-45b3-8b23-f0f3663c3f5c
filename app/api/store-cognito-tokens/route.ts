import { NextRequest } from 'next/server';
import { validateSession, createErrorResponse } from '../_utils/auth';
import { tokenStorage, testRedisConnection } from '@/lib/redis';

/**
 * POST /api/store-cognito-tokens
 * Stores Cognito tokens for the authenticated user
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const ip =
    request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

  console.log(`🚀 [API] POST /api/store-cognito-tokens - Request ID: ${requestId}`);
  console.log(`📍 [API] Client IP: ${ip}`);
  console.log(`🕐 [API] Request started at: ${new Date().toISOString()}`);

  try {
    // Validate user session
    console.log(`🔐 [API] Validating user session...`);
    const user = await validateSession(request);
    if (!user) {
      const duration = Date.now() - startTime;
      console.log(`❌ [API] Unauthorized request (${duration}ms) - Request ID: ${requestId}`);
      return createErrorResponse('Unauthorized', 401);
    }

    console.log(`✅ [API] User authenticated: ${user.email}`);

    // Get request body
    console.log(`📥 [API] Parsing request body...`);
    const body = await request.json();
    const { cognitoRefreshToken, cognitoAccessToken, cognitoIdToken } = body;

    console.log(`📊 [API] Token data received:`, {
      hasRefreshToken: !!cognitoRefreshToken,
      hasAccessToken: !!cognitoAccessToken,
      hasIdToken: !!cognitoIdToken,
      refreshTokenLength: cognitoRefreshToken?.length || 0,
      accessTokenLength: cognitoAccessToken?.length || 0,
      idTokenLength: cognitoIdToken?.length || 0,
    });

    if (!cognitoRefreshToken) {
      console.log(`❌ [API] Missing refresh token - Request ID: ${requestId}`);
      return createErrorResponse('Cognito refresh token is required', 400);
    }

    // Store tokens for the user using Redis
    const userId = user.userId || user.username;
    console.log(`💾 [API] Storing tokens for user: ${userId}`);

    const tokenData = {
      refreshToken: cognitoRefreshToken,
      accessToken: cognitoAccessToken || '',
      idToken: cognitoIdToken || '',
      email: user.email,
    };

    console.log(`📊 [API] Token data being stored in Redis:`, {
      userId,
      email: tokenData.email,
      refreshTokenLength: tokenData.refreshToken.length,
      accessTokenLength: tokenData.accessToken.length,
      idTokenLength: tokenData.idToken.length,
      refreshTokenPreview: tokenData.refreshToken.substring(0, 20) + '...',
      accessTokenPreview: tokenData.accessToken
        ? tokenData.accessToken.substring(0, 20) + '...'
        : 'empty',
      idTokenPreview: tokenData.idToken ? tokenData.idToken.substring(0, 20) + '...' : 'empty',
    });

    const storeStartTime = Date.now();
    await tokenStorage.storeTokens(userId, tokenData);

    const storeDuration = Date.now() - storeStartTime;
    const totalDuration = Date.now() - startTime;

    console.log(`✅ [API] Tokens stored successfully in ${storeDuration}ms`);
    console.log(`🔧 [AUDIT] Cognito tokens stored for user: ${user.email} (${userId})`);
    console.log(`📊 [API] Request completed in ${totalDuration}ms`);

    // Verify storage by retrieving the tokens
    console.log(`🔍 [API] Verifying token storage...`);
    try {
      const retrievedTokens = await tokenStorage.getTokens(userId);
      if (retrievedTokens) {
        console.log(`✅ [API] Token verification successful:`, {
          storedEmail: retrievedTokens.email,
          storedRefreshTokenLength: retrievedTokens.refreshToken.length,
          storedAccessTokenLength: retrievedTokens.accessToken.length,
          storedIdTokenLength: retrievedTokens.idToken.length,
          storedAt: new Date(retrievedTokens.storedAt).toISOString(),
        });
      } else {
        console.error(`❌ [API] Token verification failed - no tokens found after storage`);
      }
    } catch (verifyError) {
      console.error(`❌ [API] Token verification error:`, verifyError);
    }

    return Response.json({
      success: true,
      message: 'Tokens stored successfully',
      userId: userId,
      requestId,
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ [API] Error storing Cognito tokens (${duration}ms):`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      requestId,
      timestamp: new Date().toISOString(),
    });

    return createErrorResponse('Internal server error', 500);
  }
}

/**
 * GET /api/store-cognito-tokens
 * Retrieves stored tokens for the authenticated user (for debugging)
 */
export async function GET(request: NextRequest) {
  try {
    // Validate user session
    const user = await validateSession(request);
    if (!user) {
      return createErrorResponse('Unauthorized', 401);
    }

    const userId = user.userId || user.username;
    const tokens = await tokenStorage.getTokens(userId);

    if (!tokens) {
      return Response.json({
        hasTokens: false,
        message: 'No tokens found for user',
      });
    }

    return Response.json({
      hasTokens: true,
      storedAt: new Date(tokens.storedAt).toISOString(),
      hasRefreshToken: !!tokens.refreshToken,
      hasAccessToken: !!tokens.accessToken,
      hasIdToken: !!tokens.idToken,
      email: tokens.email,
    });
  } catch (error) {
    console.error('Error retrieving token info:', error);
    return createErrorResponse('Internal server error', 500);
  }
}

/**
 * Export function to get stored refresh token (used by auth utils)
 */
export async function getStoredRefreshTokenForUser(userId: string): Promise<string | null> {
  try {
    return await tokenStorage.getRefreshToken(userId);
  } catch (error) {
    console.error('Error getting refresh token:', error);
    return null;
  }
}
