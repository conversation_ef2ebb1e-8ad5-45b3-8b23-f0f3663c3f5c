# Development Guide - Authentication

## Development Environment Setup

### Prerequisites

1. **Node.js 18+**
2. **AWS Account with Cognito access**
3. **AWS CLI configured** (optional but recommended)

### Local Development Setup

1. **Clone and install dependencies:**

   ```bash
   git clone <repository>
   cd partner-dashboard
   npm install
   ```

2. **Create environment file:**

   ```bash
   cp .env.example .env.local
   ```

3. **Configure AWS Cognito:**
   - Create User Pool in AWS Console
   - Configure app client
   - Update `.env.local` with your values

4. **Start development server:**
   ```bash
   npm run dev
   ```

## File Structure

```
├── app/
│   ├── auth/
│   │   ├── actions.ts           # Server actions for auth operations
│   │   ├── signin/page.tsx      # Sign-in page
│   │   ├── signup/page.tsx      # Sign-up page
│   │   └── verify/page.tsx      # Email verification page
│   ├── layout.tsx               # Root layout
│   └── middleware.ts            # Route protection middleware
├── components/
│   └── auth/
│       ├── sign-in-form.tsx     # Sign-in form component
│       ├── sign-up-form.tsx     # Sign-up form component
│       ├── verify-form.tsx      # Verification form component
│       └── logout-button.tsx    # Logout button component
├── lib/
│   ├── amplify-config.ts        # Amplify configuration
│   └── auth.ts                  # Auth utility functions
└── docs/
    └── auth/
        ├── README.md            # Main documentation
        ├── SETUP.md             # Setup instructions
        └── DEVELOPMENT.md       # This file
```

## Development Workflow

### 1. Making Changes

When making changes to the authentication system:

1. **Identify the component** (server action, middleware, component)
2. **Make changes** following the patterns established
3. **Test locally** with real Cognito setup
4. **Update tests** if applicable
5. **Update documentation** if needed

### 2. Adding New Auth Features

For new authentication features:

1. **Server Actions**: Add to `app/auth/actions.ts`
2. **UI Components**: Create in `components/auth/`
3. **Pages**: Add to `app/auth/`
4. **Utilities**: Add to `lib/auth.ts`

### 3. Code Style Guidelines

#### Server Actions

```typescript
export async function myAuthAction(prevState: any, formData: FormData): Promise<AuthResult> {
  const field = formData.get('field') as string;

  try {
    configureAmplifyServer();

    // Validation
    if (!field) {
      return { success: false, error: 'Field is required' };
    }

    // Cognito operation
    const result = await cognitoOperation();

    return { success: true };
  } catch (error: any) {
    console.error('Operation error:', error);

    switch (error.name) {
      case 'SpecificError':
        return { success: false, error: 'User-friendly message' };
      default:
        return { success: false, error: 'Generic error message' };
    }
  }
}
```

#### React Components

```typescript
'use client';

import { useActionState } from 'react';
import { useEffect } from 'react';

const initialState = {
  success: false,
  error: undefined
};

export function MyAuthForm() {
  const [state, formAction] = useActionState(myAuthAction, initialState);

  useEffect(() => {
    if (state.success) {
      // Handle success
    }
  }, [state.success]);

  return (
    <form action={formAction}>
      {/* Form content */}
    </form>
  );
}
```

## Testing Strategy

### 1. Local Testing

Test all authentication flows locally:

```bash
# Start dev server
npm run dev

# Test flows:
# 1. Sign up new user
# 2. Verify email
# 3. Sign in
# 4. Access protected routes
# 5. Sign out
```

### 2. Unit Testing

Create tests for server actions:

```typescript
// __tests__/auth.test.ts
import { authenticateUser } from '@/app/auth/actions';

describe('Authentication', () => {
  it('should authenticate valid user', async () => {
    const formData = new FormData();
    formData.append('email', '<EMAIL>');
    formData.append('password', 'validpassword');

    const result = await authenticateUser({}, formData);

    expect(result.success).toBe(true);
  });
});
```

### 3. Integration Testing

Use Cypress for end-to-end testing:

```typescript
// cypress/e2e/auth.cy.ts
describe('Authentication Flow', () => {
  it('should sign up, verify, and sign in', () => {
    cy.visit('/auth/signup');
    cy.get('[name="name"]').type('Test User');
    cy.get('[name="email"]').type('<EMAIL>');
    cy.get('[name="password"]').type('password123');
    cy.get('[name="confirmPassword"]').type('password123');
    cy.get('button[type="submit"]').click();

    // Continue with verification and sign-in flow
  });
});
```

## Debugging

### 1. Enable Debug Logging

Add to `.env.local`:

```env
NEXT_PUBLIC_DEBUG_AUTH=true
```

### 2. Common Debug Points

- **Server Actions**: Add `console.log` for form data and errors
- **Middleware**: Log session checks and redirects
- **Components**: Log state changes and form submissions

### 3. Browser DevTools

- **Network Tab**: Check request/response for auth operations
- **Application Tab**: Inspect cookies for session data
- **Console**: Look for error messages and debug logs

## Environment Configuration

### Development Environment

```env
# .env.local
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-1_devpoolid
NEXT_PUBLIC_COGNITO_CLIENT_ID=devclientid
NEXT_PUBLIC_AWS_REGION=us-east-1
NODE_ENV=development
NEXT_PUBLIC_DEBUG_AUTH=true
```

### Staging Environment

```env
# .env.staging
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-1_stagingpoolid
NEXT_PUBLIC_COGNITO_CLIENT_ID=stagingclientid
NEXT_PUBLIC_AWS_REGION=us-east-1
NODE_ENV=staging
```

### Production Environment

```env
# .env.production
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-1_prodpoolid
NEXT_PUBLIC_COGNITO_CLIENT_ID=prodclientid
NEXT_PUBLIC_AWS_REGION=us-east-1
NODE_ENV=production
```

## Performance Considerations

### 1. Server-Side Optimization

- **Minimize Amplify configuration calls**
- **Cache session validation results**
- **Use HTTP-only cookies for session storage**

### 2. Client-Side Optimization

- **Use `useActionState` for form management**
- **Minimize client-side JavaScript**
- **Implement proper loading states**

### 3. Bundle Size

- **Import only necessary Amplify modules**
- **Use dynamic imports for auth components**
- **Tree-shake unused code**

## Security Considerations

### 1. Development Security

- **Never commit `.env.local` files**
- **Use different User Pools for different environments**
- **Regularly rotate development credentials**

### 2. Code Security

- **Validate all inputs server-side**
- **Use parameterized queries**
- **Sanitize error messages**

### 3. Infrastructure Security

- **Enable HTTPS in all environments**
- **Configure proper CORS settings**
- **Implement rate limiting**

## Deployment

### 1. Environment Variables

Ensure all required environment variables are set in your deployment platform:

```bash
# Vercel
vercel env add NEXT_PUBLIC_COGNITO_USER_POOL_ID

# Netlify
netlify env:set NEXT_PUBLIC_COGNITO_USER_POOL_ID value

# AWS Amplify
amplify env add
```

### 2. Build Verification

Test the build process:

```bash
npm run build
npm start
```

### 3. Production Testing

After deployment:

1. Test all authentication flows
2. Verify environment variables
3. Check error handling
4. Test session persistence

## Monitoring

### 1. Application Monitoring

- **Implement error tracking** (Sentry, Bugsnag)
- **Monitor authentication success/failure rates**
- **Track user registration flow completion**

### 2. AWS Monitoring

- **Enable CloudWatch logs** for Cognito
- **Set up alarms** for authentication failures
- **Monitor User Pool metrics**

### 3. Performance Monitoring

- **Track authentication operation timing**
- **Monitor server action performance**
- **Implement user experience metrics**

## Troubleshooting Common Issues

### 1. Development Issues

**Issue**: "Amplify not configured"

```bash
# Solution: Check environment variables
cat .env.local
# Restart development server
npm run dev
```

**Issue**: CORS errors

```bash
# Solution: Check Cognito CORS settings
# Ensure domain is whitelisted
```

### 2. Build Issues

**Issue**: Environment variables not available

```bash
# Solution: Ensure NEXT_PUBLIC_ prefix
# Check build environment variable injection
```

### 3. Runtime Issues

**Issue**: Session not persisting

```bash
# Check cookie settings
# Verify domain configuration
# Ensure HTTPS in production
```

## Contributing Guidelines

### 1. Code Quality

- **Follow TypeScript best practices**
- **Use ESLint and Prettier**
- **Write comprehensive tests**
- **Document new features**

### 2. Pull Request Process

1. **Create feature branch** from main
2. **Make changes** following style guide
3. **Add/update tests**
4. **Update documentation**
5. **Create pull request** with detailed description

### 3. Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests pass
- [ ] Documentation updated
- [ ] Security considerations addressed
- [ ] Performance impact assessed

## Resources

- [Next.js Server Actions Documentation](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions)
- [AWS Cognito Best Practices](https://docs.aws.amazon.com/cognito/latest/developerguide/cognito-user-pools-security-best-practices.html)
- [React Hook Form Documentation](https://react-hook-form.com/get-started)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
