# Authentication System Documentation

## Overview

This application uses AWS Cognito for authentication with AWS Amplify, implemented using Next.js 13+ App Router with server-side rendering and server actions. The system provides a secure, scalable authentication solution with comprehensive error handling and type safety.

## Table of Contents

1. [Architecture](#architecture)
2. [Setup & Configuration](#setup--configuration)
3. [Authentication Flow](#authentication-flow)
4. [API Reference](#api-reference)
5. [Components](#components)
6. [Security Features](#security-features)
7. [Error Handling](#error-handling)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)
10. [Best Practices](#best-practices)

## Architecture

### System Design

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Side   │    │   Server Side   │    │   AWS Cognito   │
│                 │    │                 │    │                 │
│ • Forms         │────│ • Server Actions│────│ • User Pool     │
│ • UI Components │    │ • Middleware    │    │ • Authentication│
│ • State Mgmt    │    │ • Session Mgmt  │    │ • User Mgmt     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Components

- **Server Actions**: Handle all authentication operations server-side
- **Middleware**: Protects routes and manages redirects
- **Session Management**: Secure HTTP-only cookies for session persistence
- **UI Components**: React components for auth forms
- **Error Handling**: Comprehensive error management with user-friendly messages

## Setup & Configuration

### 1. Environment Variables

Create a `.env.local` file in your project root:

```env
# AWS Cognito Configuration
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx
NEXT_PUBLIC_COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID=us-east-1:xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx

# AWS Region
NEXT_PUBLIC_AWS_REGION=us-east-1

# Environment
NODE_ENV=development
```

### 2. AWS Cognito User Pool Configuration

#### Required Settings:

- **Sign-in options**: Email
- **User name requirements**: Email address
- **Password policy**: Cognito defaults or custom
- **Multi-factor authentication**: Optional
- **User account recovery**: Email only

#### App Client Settings:

```json
{
  "appType": "Public client",
  "authenticationFlows": ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"],
  "generateClientSecret": false,
  "oAuthFlows": []
}
```

### 3. Required User Attributes

- **email** (required, mutable)
- **name** (required, mutable)
- **preferred_username** (optional, mutable)

## Authentication Flow

### 1. Sign Up Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Form
    participant SA as Server Action
    participant C as Cognito
    participant E as Email

    U->>F: Fill signup form
    F->>SA: Submit form data
    SA->>C: signUp()
    C->>E: Send verification email
    C->>SA: Return confirmation required
    SA->>F: Redirect to verify page
    U->>F: Enter verification code
    F->>SA: Submit verification
    SA->>C: confirmSignUp()
    C->>SA: Confirmation success
    SA->>F: Redirect to signin
```

### 2. Sign In Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Form
    participant SA as Server Action
    participant C as Cognito
    participant S as Session

    U->>F: Enter credentials
    F->>SA: Submit form data
    SA->>C: signIn()
    C->>SA: Return auth result
    SA->>S: Set session cookie
    SA->>F: Return success + redirect
    F->>U: Redirect to dashboard
```

### 3. Protected Route Access

```mermaid
sequenceDiagram
    participant U as User
    participant M as Middleware
    participant S as Session
    participant P as Protected Page

    U->>M: Request protected route
    M->>S: Check session cookie
    alt Session valid
        S->>M: Session data
        M->>P: Allow access
        P->>U: Render page
    else No session
        M->>U: Redirect to /auth/signin
    end
```

## API Reference

### Server Actions

#### `authenticateUser(prevState, formData)`

Authenticates a user with email and password.

**Parameters:**

- `prevState`: Previous form state
- `formData`: Form data containing email and password

**Returns:**

```typescript
{
  success: boolean;
  error?: string;
  requiresVerification?: boolean;
  redirectTo?: string;
}
```

**Example:**

```tsx
import { authenticateUser } from '@/app/auth/actions';

const [state, formAction] = useActionState(authenticateUser, initialState);
```

#### `registerUser(prevState, formData)`

Registers a new user account.

**Parameters:**

- `prevState`: Previous form state
- `formData`: Form data containing name, email, password, confirmPassword

**Returns:**

```typescript
{
  success: boolean;
  error?: string;
  nextStep?: string;
}
```

#### `verifyEmail(prevState, formData)`

Verifies user email with confirmation code.

**Parameters:**

- `prevState`: Previous form state
- `formData`: Form data containing email and verification code

#### `logout()`

Logs out the current user and redirects to sign-in page.

**Example:**

```tsx
import { logout } from '@/app/auth/actions';

const handleLogout = async () => {
  await logout();
};
```

### Utility Functions

#### `getSession()`

Retrieves the current user session from cookies.

**Returns:**

```typescript
{
  username: string;
  signedIn: boolean;
  timestamp: number;
} | null
```

#### `requireAuth()`

Server-side function to require authentication. Redirects to sign-in if not authenticated.

#### `requireNoAuth()`

Server-side function to redirect authenticated users away from auth pages.

## Components

### Authentication Forms

#### `SignInForm`

Sign-in form component with email and password fields.

**Features:**

- Form validation
- Error display
- Loading states
- Redirect handling

**Usage:**

```tsx
import { SignInForm } from '@/components/auth/sign-in-form';

<SignInForm />;
```

#### `SignUpForm`

Sign-up form component with name, email, and password fields.

**Features:**

- Password confirmation
- Name field requirement
- Validation
- Error handling

#### `VerifyForm`

Email verification form component.

**Features:**

- Email and code inputs
- Validation
- Error handling

#### `LogoutButton`

Logout button component with customizable styling.

**Props:**

```typescript
interface LogoutButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  children?: React.ReactNode;
}
```

**Usage:**

```tsx
import { LogoutButton } from '@/components/auth/logout-button';

<LogoutButton variant="destructive">Sign Out</LogoutButton>;
```

### Authentication Pages

#### `/auth/signin`

- Server-side rendered sign-in page
- Redirects authenticated users to dashboard
- Handles redirect parameter for post-auth navigation

#### `/auth/signup`

- Server-side rendered sign-up page
- Redirects to verification page on successful registration

#### `/auth/verify`

- Email verification page
- Redirects to sign-in on successful verification

## Security Features

### 1. HTTP-Only Cookies

Session data is stored in secure, HTTP-only cookies that are:

- Not accessible via JavaScript
- Automatically included in requests
- Secure in production (HTTPS only)
- SameSite protection

### 2. CSRF Protection

- Server actions provide built-in CSRF protection
- Form submissions are validated server-side

### 3. Session Management

- Sessions expire after 7 days
- Automatic cleanup of expired sessions
- Secure session invalidation on logout

### 4. Route Protection

Middleware protects routes by:

- Checking session validity
- Redirecting unauthenticated users
- Preserving intended destination

### 5. Input Validation

- Server-side validation of all inputs
- Password strength requirements
- Email format validation
- XSS prevention through proper escaping

## Error Handling

### Error Categories

#### Authentication Errors

- `UserNotConfirmedException`: User needs email verification
- `NotAuthorizedException`: Invalid credentials
- `UserNotFoundException`: Account doesn't exist
- `PasswordResetRequiredException`: Password reset required

#### Registration Errors

- `UsernameExistsException`: Email already registered
- `InvalidPasswordException`: Password doesn't meet requirements
- `InvalidParameterException`: Invalid email format

#### Verification Errors

- `CodeMismatchException`: Invalid verification code
- `ExpiredCodeException`: Verification code expired
- `NotAuthorizedException`: User already verified

### Error Display

Errors are displayed using the Alert component with appropriate variants:

```tsx
{
  state.error && (
    <Alert variant={state.requiresVerification ? 'default' : 'destructive'}>
      <AlertDescription>{state.error}</AlertDescription>
    </Alert>
  );
}
```

## Testing

### Manual Testing Checklist

#### Sign Up Flow

- [ ] Valid registration with name, email, password
- [ ] Password mismatch validation
- [ ] Duplicate email handling
- [ ] Email verification process
- [ ] Invalid verification code handling

#### Sign In Flow

- [ ] Valid credentials authentication
- [ ] Invalid credentials handling
- [ ] Unverified user handling
- [ ] Session persistence
- [ ] Redirect functionality

#### Protected Routes

- [ ] Unauthenticated access redirects to sign-in
- [ ] Authenticated access works
- [ ] Session expiration handling

#### Logout

- [ ] Session cleanup
- [ ] Redirect to sign-in page
- [ ] Re-authentication required

### Unit Testing

Example test for authentication:

```typescript
import { authenticateUser } from '@/app/auth/actions';

describe('authenticateUser', () => {
  it('should authenticate valid user', async () => {
    const formData = new FormData();
    formData.append('email', '<EMAIL>');
    formData.append('password', 'validpassword');

    const result = await authenticateUser({}, formData);

    expect(result.success).toBe(true);
    expect(result.redirectTo).toBe('/');
  });
});
```

## Troubleshooting

### Common Issues

#### 1. "Amplify has not been configured"

**Cause**: Environment variables not set or Amplify not configured properly.

**Solution**:

1. **Check Environment Variables**: Ensure you have these in `.env.local`:

   ```env
   NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-1_XXXXXXXXX
   NEXT_PUBLIC_COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
   ```

2. **Restart Development Server**: After adding environment variables:

   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

3. **Verify Cognito Configuration**: Ensure your AWS Cognito User Pool has:
   - Authentication flows: `ALLOW_USER_SRP_AUTH` enabled
   - No app client secret generated
   - Email verification enabled

4. **Check Console Logs**: Look for specific configuration errors in browser console

#### 2. "NotAuthorizedException: Incorrect username or password"

**Cause**: Multiple possible reasons for this error.

**Solution**:

1. **Check if user is verified**: User must verify email before signing in
   - Go to `/auth/verify` and enter verification code
   - Check spam folder for verification email

2. **Verify credentials**: Ensure email and password are correct
   - Email is case-sensitive
   - Password must match exactly

3. **Check User Pool configuration**: In AWS Cognito:
   - Ensure user exists in User Pool
   - Check if account is enabled
   - Verify authentication flows are enabled

4. **Reset password if needed**: Use forgot password flow if password is forgotten

#### 3. "User is not confirmed"

**Cause**: User hasn't verified their email address.

**Solution**:

- Direct user to verification page
- Check spam folder for verification email
- Resend verification code if needed

#### 4. "Invalid authentication flow"

**Cause**: Cognito User Pool app client not configured correctly.

**Solution**:

- Enable `ALLOW_USER_SRP_AUTH` in app client settings
- Ensure no client secret is generated
- Verify authentication flows configuration

#### 5. Session not persisting

**Cause**: Cookie settings or domain issues.

**Solution**:

- Check cookie settings in server actions
- Verify domain configuration
- Ensure HTTPS in production

#### 6. Infinite redirect loops

**Cause**: Middleware configuration issues.

**Solution**:

- Check public paths in middleware
- Verify session validation logic
- Ensure proper redirect handling

### Quick Debug Steps

When experiencing authentication issues, follow these steps:

1. **Check Environment Variables**:

   ```bash
   echo $NEXT_PUBLIC_COGNITO_USER_POOL_ID
   echo $NEXT_PUBLIC_COGNITO_CLIENT_ID
   ```

2. **Check User Status in AWS Console**:
   - Go to AWS Cognito User Pools
   - Find your user
   - Check status (CONFIRMED vs UNCONFIRMED)

3. **Test with Console Logs**:
   - Open browser dev tools
   - Look for Amplify configuration errors
   - Check server action responses

4. **Verify User Pool Settings**:
   - Authentication flows: SRP enabled
   - App client: No secret
   - Verification: Email enabled

### Debug Mode

Enable debug logging by adding to environment:

```env
NEXT_PUBLIC_DEBUG_AUTH=true
```

This will provide detailed console logs for authentication operations.

## Best Practices

### 1. Security

- **Never expose sensitive data**: Keep User Pool secrets server-side only
- **Use HTTPS in production**: Ensure all auth traffic is encrypted
- **Implement proper CORS**: Configure Cognito CORS settings appropriately
- **Regular security audits**: Review authentication flows regularly

### 2. User Experience

- **Clear error messages**: Provide helpful, non-technical error messages
- **Loading states**: Show loading indicators during auth operations
- **Redirect preservation**: Save intended destination for post-auth redirect
- **Responsive design**: Ensure auth forms work on all devices

### 3. Development

- **Environment separation**: Use different User Pools for dev/staging/prod
- **Error logging**: Implement comprehensive error logging
- **Type safety**: Use TypeScript for all auth-related code
- **Testing**: Implement both unit and integration tests

### 4. Performance

- **Server-side rendering**: Leverage SSR for auth pages
- **Minimal client JavaScript**: Keep auth logic server-side when possible
- **Caching**: Implement appropriate caching strategies
- **Bundle optimization**: Use dynamic imports for auth components

### 5. Monitoring

- **CloudWatch integration**: Monitor Cognito metrics
- **Error tracking**: Implement error tracking (e.g., Sentry)
- **User analytics**: Track authentication success/failure rates
- **Performance monitoring**: Monitor auth operation performance

## Migration Guide

### From Client-Side Auth

If migrating from a client-side authentication system:

1. **Move auth logic to server actions**
2. **Replace client state with server sessions**
3. **Update components to use server actions**
4. **Implement proper middleware**
5. **Test all authentication flows**

### Version Updates

When updating Amplify versions:

1. **Check breaking changes** in Amplify documentation
2. **Update import statements** if API changes
3. **Test authentication flows** thoroughly
4. **Update configuration** if needed

## Contributing

When contributing to the authentication system:

1. **Follow security best practices**
2. **Add appropriate tests**
3. **Update documentation**
4. **Review error handling**
5. **Test with real Cognito setup**

## Resources

- [AWS Cognito Documentation](https://docs.aws.amazon.com/cognito/)
- [AWS Amplify Documentation](https://docs.amplify.aws/)
- [Next.js App Router Documentation](https://nextjs.org/docs/app)
- [React Hook Form Documentation](https://react-hook-form.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
