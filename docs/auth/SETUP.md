# AWS Cognito Authentication Setup

This guide provides step-by-step instructions for setting up AWS Cognito authentication in your Next.js application.

## Prerequisites

- AWS Account
- Next.js 13+ with App Router
- AWS CLI configured (optional but recommended)

## Step 1: Create Cognito User Pool

1. **Navigate to AWS Cognito Console**
   - Go to AWS Console → Cognito → User pools
   - Click "Create user pool"

2. **Configure User Pool**
   - Sign-in options: Email
   - Password policy: Default or custom
   - Multi-factor authentication: Optional
   - User account recovery: Email only

3. **App Integration**
   - User pool name: `partner-dashboard-users`
   - App client name: `partner-dashboard-client`
   - Generate app client secret: **NO** (for web apps)
   - Authentication flows:
     - ✅ ALLOW_USER_SRP_AUTH
     - ✅ ALLOW_REFRESH_TOKEN_AUTH

4. **Note the following values:**
   - User Pool ID (e.g., `us-east-1_xxxxxxxxx`)
   - App Client ID (e.g., `xxxxxxxxxxxxxxxxxxxxxxxxxx`)

## Step 2: Environment Variables

Create a `.env.local` file in your project root:

```env
# AWS Cognito Configuration
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx
NEXT_PUBLIC_COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID=us-east-1:xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx

# AWS Region
NEXT_PUBLIC_AWS_REGION=us-east-1

# Environment
NODE_ENV=development
```

## Step 3: Test Authentication Flow

1. **Start the development server:**

   ```bash
   npm run dev
   ```

2. **Test the authentication flow:**
   - Navigate to `/auth/signup` to create an account
   - Check your email for verification code
   - Verify your account at `/auth/verify`
   - Sign in at `/auth/signin`

## Step 4: AWS Cognito User Pool Settings

### Recommended Settings:

**Sign-in experience:**

- Cognito user pool sign-in options: Email
- User name requirements: Email address

**Security requirements:**

- Password policy: Cognito defaults
- Multi-factor authentication: Optional
- User account recovery: Email only

**Sign-up experience:**

- Self-registration: Enabled
- Cognito-assisted verification: Send email
- Required attributes: Email
- Optional attributes: None

**Message delivery:**

- Email provider: Send email with Cognito
- SES Region: Same as User Pool region

### App client settings:

- App type: Public client
- Authentication flows:
  - ✅ ALLOW_USER_SRP_AUTH
  - ✅ ALLOW_REFRESH_TOKEN_AUTH
- OAuth 2.0 flows: None (for now)
- Generate client secret: **NO**

## Step 5: Testing

Test the following scenarios:

1. **Sign Up Flow:**

   ```
   /auth/signup → Email verification → /auth/verify → /auth/signin
   ```

2. **Sign In Flow:**

   ```
   /auth/signin → Dashboard (/)
   ```

3. **Protected Routes:**
   - Try accessing `/` without authentication
   - Should redirect to `/auth/signin`

4. **Logout:**
   - Use the logout functionality
   - Should clear session and redirect to sign-in

## Troubleshooting

### Common Issues:

1. **"Amplify has not been configured" Error:**
   - Ensure environment variables are set correctly
   - Restart the development server

2. **"User is not confirmed" Error:**
   - User needs to verify their email
   - Check spam folder for verification email

3. **"Invalid authentication flow" Error:**
   - Check User Pool app client settings
   - Ensure ALLOW_USER_SRP_AUTH is enabled

4. **Network Errors:**
   - Verify AWS region in environment variables
   - Check User Pool and App Client IDs

### Debug Mode:

Add this to your environment for debugging:

```env
NEXT_PUBLIC_DEBUG_AUTH=true
```

## Security Best Practices

1. **Environment Variables:**
   - Never commit `.env.local` to version control
   - Use different User Pools for different environments

2. **HTTPS:**
   - Always use HTTPS in production
   - Configure secure cookie settings

3. **Session Management:**
   - Sessions expire after 7 days
   - Automatic cleanup of expired sessions

4. **Error Handling:**
   - Generic error messages to prevent information leakage
   - Detailed logging for debugging

## Production Deployment

1. **Environment Variables:**
   - Set all required environment variables in your hosting platform
   - Use production User Pool IDs

2. **Domain Configuration:**
   - Configure custom domain if needed
   - Update CORS settings in Cognito

3. **Monitoring:**
   - Enable CloudWatch logs
   - Monitor authentication metrics

## API Integration

The authentication system provides server actions for:

- `authenticateUser()` - Sign in
- `registerUser()` - Sign up
- `verifyEmail()` - Email verification
- `logout()` - Sign out
- `getSession()` - Get current session

All actions include comprehensive error handling and type safety.
