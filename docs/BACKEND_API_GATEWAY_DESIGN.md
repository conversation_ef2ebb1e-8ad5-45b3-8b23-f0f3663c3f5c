# Backend API Gateway Design

## Overview

This document outlines the design for a backend API gateway that handles authentication, token management, and proxying requests to third-party APIs. This approach centralizes authentication logic and provides better security than client-side token management.

## Architecture Benefits

### 🔒 **Security Advantages**

- **No Client-Side Secrets**: Refresh tokens and API keys never exposed to frontend
- **Centralized Auth**: Single point for authentication logic and token refresh
- **Session-Based**: Uses secure HTTP-only cookies instead of localStorage tokens
- **Token Rotation**: Backend handles automatic token refresh without client involvement

### 🏗️ **Architectural Advantages**

- **API Abstraction**: Frontend doesn't need to know about multiple API endpoints
- **Rate Limiting**: Centralized rate limiting and request throttling
- **Caching**: Backend can cache responses and implement smart caching strategies
- **Monitoring**: Single point for API usage monitoring and logging

## System Architecture

```mermaid
graph TB
    subgraph "Frontend (Next.js)"
        A[React Components]
        B[API Client]
        C[Session Cookies]
    end

    subgraph "Your Backend API Gateway"
        D[Auth Middleware]
        E[Session Validation]
        F[Token Management]
        G[Request Proxy]
        H[Response Cache]
        I[Rate Limiting]
    end

    subgraph "External Services"
        J[QBraid API]
        K[Other 3rd Party APIs]
        L[AWS Cognito]
    end

    subgraph "Storage"
        M[Redis Cache]
        N[Token Store]
        O[Session Store]
    end

    A --> B
    B --> C
    B --> D
    D --> E
    E --> L
    D --> F
    F --> N
    F --> G
    G --> H
    H --> M
    G --> J
    G --> K
    I --> D
    E --> O
```

## Backend Implementation Guide

### 1. Session Validation Middleware

```python
# Example in Python/FastAPI
from fastapi import HTTPException, Depends
from jose import jwt, JWTError
import redis

class AuthMiddleware:
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.session_secret = os.getenv('SESSION_SECRET')

    async def validate_session(self, authorization: str = Header(None)):
        """Validate JWT session token from frontend"""
        if not authorization or not authorization.startswith('Bearer '):
            raise HTTPException(401, "No valid session")

        token = authorization.replace('Bearer ', '')

        try:
            # Verify JWT signature
            payload = jwt.decode(token, self.session_secret, algorithms=['HS256'])

            # Check if session is revoked
            session_id = payload.get('jti')
            if await self.redis.get(f"revoked_session:{session_id}"):
                raise HTTPException(401, "Session revoked")

            return payload
        except JWTError:
            raise HTTPException(401, "Invalid session")

    async def get_user_tokens(self, user_id: str):
        """Get stored refresh tokens for user"""
        tokens = await self.redis.hgetall(f"user_tokens:{user_id}")
        return {
            'qbraid_refresh_token': tokens.get('qbraid_refresh_token'),
            'qbraid_access_token': tokens.get('qbraid_access_token'),
            'email': tokens.get('email'),
        }

    async def refresh_tokens_if_needed(self, user_id: str):
        """Check and refresh tokens if they're about to expire"""
        tokens = await self.get_user_tokens(user_id)

        # Check if access token is expired/expiring
        if self.is_token_expiring(tokens['qbraid_access_token']):
            new_tokens = await self.refresh_qbraid_tokens(
                tokens['qbraid_refresh_token']
            )

            # Store new tokens
            await self.redis.hset(f"user_tokens:{user_id}", mapping=new_tokens)
            return new_tokens

        return tokens
```

### 2. Token Management System

```python
class TokenManager:
    def __init__(self):
        self.qbraid_client_id = os.getenv('QBRAID_CLIENT_ID')
        self.qbraid_client_secret = os.getenv('QBRAID_CLIENT_SECRET')

    async def store_user_tokens(self, user_id: str, email: str, refresh_token: str):
        """Store user tokens after successful Cognito auth"""
        # Get initial access token
        access_token = await self.get_access_token_from_refresh(refresh_token)

        tokens = {
            'email': email,
            'qbraid_refresh_token': refresh_token,
            'qbraid_access_token': access_token,
            'stored_at': datetime.utcnow().isoformat(),
        }

        await self.redis.hset(f"user_tokens:{user_id}", mapping=tokens)

        # Set expiration for cleanup
        await self.redis.expire(f"user_tokens:{user_id}", 86400 * 30)  # 30 days

    async def refresh_qbraid_tokens(self, refresh_token: str):
        """Refresh QBraid access token"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                'https://api.qbraid.com/oauth/token',
                data={
                    'grant_type': 'refresh_token',
                    'refresh_token': refresh_token,
                    'client_id': self.qbraid_client_id,
                    'client_secret': self.qbraid_client_secret,
                }
            )

            if response.status_code == 200:
                token_data = response.json()
                return {
                    'qbraid_access_token': token_data['access_token'],
                    'qbraid_refresh_token': token_data.get('refresh_token', refresh_token),
                    'expires_at': (datetime.utcnow() + timedelta(seconds=token_data['expires_in'])).isoformat(),
                }
            else:
                raise HTTPException(401, "Failed to refresh tokens")
```

### 3. API Proxy Endpoints

```python
@app.get("/api/quantum-devices")
async def get_quantum_devices(
    session: dict = Depends(auth_middleware.validate_session)
):
    """Proxy request to QBraid API with proper authentication"""
    user_id = session['userId']

    # Get and refresh tokens if needed
    tokens = await auth_middleware.refresh_tokens_if_needed(user_id)

    # Make request to QBraid API
    async with httpx.AsyncClient() as client:
        response = await client.get(
            'https://api.qbraid.com/api/quantum-devices',
            headers={
                'Authorization': f"Bearer {tokens['qbraid_access_token']}",
                'email': tokens['email'],
                'Content-Type': 'application/json',
            }
        )

        # Cache response if successful
        if response.status_code == 200:
            await redis_client.setex(
                f"devices_cache:{user_id}",
                300,  # 5 minutes
                response.text
            )

        return response.json()

@app.put("/api/quantum-devices/edit")
async def update_quantum_device(
    device_data: dict,
    device_id: str = Query(...),
    session: dict = Depends(auth_middleware.validate_session)
):
    """Proxy device update request"""
    user_id = session['userId']
    tokens = await auth_middleware.refresh_tokens_if_needed(user_id)

    async with httpx.AsyncClient() as client:
        response = await client.put(
            f'https://api.qbraid.com/api/quantum-devices/edit?id={device_id}',
            json=device_data,
            headers={
                'Authorization': f"Bearer {tokens['qbraid_access_token']}",
                'email': tokens['email'],
                'Content-Type': 'application/json',
            }
        )

        # Invalidate cache on update
        await redis_client.delete(f"devices_cache:{user_id}")

        return response.json()
```

## Cognito Integration for Token Storage

### 1. Enhanced Authentication Flow

```python
@app.post("/auth/cognito-callback")
async def handle_cognito_success(auth_data: dict):
    """Handle successful Cognito authentication and store tokens"""
    user_id = auth_data['userId']
    email = auth_data['email']

    # Get QBraid refresh token for this user
    # This could come from:
    # 1. User's initial setup/onboarding
    # 2. OAuth flow with QBraid
    # 3. Manual token entry (for existing users)

    qbraid_refresh_token = await get_user_qbraid_token(email)

    if qbraid_refresh_token:
        await token_manager.store_user_tokens(
            user_id,
            email,
            qbraid_refresh_token
        )

    return {"status": "success", "tokens_stored": bool(qbraid_refresh_token)}
```

### 2. Token Acquisition Strategies

#### Option A: OAuth Flow with QBraid

```python
@app.get("/auth/qbraid-oauth")
async def initiate_qbraid_oauth(session: dict = Depends(auth_middleware.validate_session)):
    """Initiate OAuth flow with QBraid to get refresh token"""
    state = generate_secure_token()

    # Store state for validation
    await redis_client.setex(f"oauth_state:{state}", 600, session['userId'])

    oauth_url = (
        f"https://api.qbraid.com/oauth/authorize"
        f"?client_id={QBRAID_CLIENT_ID}"
        f"&response_type=code"
        f"&scope=api_access"
        f"&state={state}"
        f"&redirect_uri={OAUTH_REDIRECT_URI}"
    )

    return {"oauth_url": oauth_url}

@app.get("/auth/qbraid-callback")
async def handle_qbraid_oauth_callback(code: str, state: str):
    """Handle QBraid OAuth callback and store tokens"""
    # Validate state
    user_id = await redis_client.get(f"oauth_state:{state}")
    if not user_id:
        raise HTTPException(400, "Invalid state")

    # Exchange code for tokens
    tokens = await exchange_code_for_tokens(code)

    # Store tokens for user
    await token_manager.store_user_tokens(
        user_id.decode(),
        tokens['email'],
        tokens['refresh_token']
    )

    return {"status": "success"}
```

#### Option B: Secure Token Storage

```python
@app.post("/auth/store-qbraid-token")
async def store_qbraid_token(
    token_data: dict,
    session: dict = Depends(auth_middleware.validate_session)
):
    """Allow users to securely store their QBraid refresh token"""
    user_id = session['userId']
    email = session['email']

    # Validate the refresh token works
    try:
        access_token = await token_manager.get_access_token_from_refresh(
            token_data['refresh_token']
        )

        await token_manager.store_user_tokens(
            user_id,
            email,
            token_data['refresh_token']
        )

        return {"status": "success", "message": "Token stored successfully"}
    except Exception as e:
        raise HTTPException(400, f"Invalid refresh token: {str(e)}")
```

## Security Considerations

### 1. Token Storage Security

- **Encrypted Storage**: Encrypt refresh tokens at rest
- **Access Control**: Only the backend can access stored tokens
- **Rotation**: Implement token rotation policies
- **Revocation**: Ability to revoke stored tokens

### 2. Session Security

- **JWT Validation**: Verify all incoming session tokens
- **Session Revocation**: Maintain revoked session list
- **Expiration**: Enforce session expiration
- **Refresh**: Implement session refresh mechanism

### 3. Rate Limiting

- **Per-User Limits**: Prevent abuse by individual users
- **Global Limits**: Protect against overall system abuse
- **API-Specific Limits**: Different limits for different endpoints
- **Graceful Degradation**: Handle rate limit exceeded scenarios

## Cognito Refresh Token Integration

### Getting Refresh Tokens from Cognito

AWS Cognito provides refresh tokens that can be used to get new access tokens. Here's how to integrate them:

#### 1. Enhanced Session Creation (Frontend)

```typescript
// In your auth actions, store Cognito tokens
export async function authenticateUser(prevState: any, formData: FormData) {
  try {
    const result = await signIn({ username: email, password });

    if (result.isSignedIn) {
      // Get Cognito tokens
      const session = await fetchAuthSession();
      const tokens = session.tokens;

      // Create your JWT session
      const sessionData = await createSession({
        username: email,
        email: email,
        userId: result.userId,
      });

      // Send Cognito refresh token to your backend for storage
      await fetch('/api/store-cognito-tokens', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionToken: sessionData,
          cognitoRefreshToken: tokens?.refreshToken?.toString(),
          cognitoAccessToken: tokens?.accessToken?.toString(),
          cognitoIdToken: tokens?.idToken?.toString(),
        }),
      });

      await setSessionCookie(sessionData);
      return { success: true };
    }
  } catch (error) {
    // Handle error
  }
}
```

#### 2. Backend Token Storage

```python
@app.post("/api/store-cognito-tokens")
async def store_cognito_tokens(token_data: dict):
    """Store Cognito tokens for the user session"""
    # Validate the session token
    session = jwt.decode(token_data['sessionToken'], SESSION_SECRET, algorithms=['HS256'])
    user_id = session['userId']

    # Store Cognito tokens
    await redis_client.hset(f"cognito_tokens:{user_id}", mapping={
        'refresh_token': token_data['cognitoRefreshToken'],
        'access_token': token_data['cognitoAccessToken'],
        'id_token': token_data['cognitoIdToken'],
        'stored_at': datetime.utcnow().isoformat(),
    })

    # Set expiration
    await redis_client.expire(f"cognito_tokens:{user_id}", 86400 * 30)  # 30 days

    return {"status": "success"}
```

#### 3. Using Cognito Tokens for QBraid API

```python
async def get_qbraid_tokens_from_cognito(user_id: str):
    """Use Cognito tokens to authenticate with QBraid API"""
    cognito_tokens = await redis_client.hgetall(f"cognito_tokens:{user_id}")

    if not cognito_tokens:
        raise HTTPException(401, "No Cognito tokens found")

    # Use Cognito ID token to authenticate with QBraid
    # (This assumes QBraid accepts Cognito tokens - adjust as needed)
    async with httpx.AsyncClient() as client:
        response = await client.post(
            'https://api.qbraid.com/auth/cognito',
            headers={
                'Authorization': f"Bearer {cognito_tokens['id_token']}",
                'Content-Type': 'application/json',
            }
        )

        if response.status_code == 200:
            qbraid_tokens = response.json()
            return qbraid_tokens
        else:
            # Refresh Cognito tokens and try again
            await refresh_cognito_tokens(user_id)
            # Retry logic here
```

## Environment Variables for Backend

```env
# Backend .env file
SESSION_SECRET=your-jwt-session-secret
REDIS_URL=redis://localhost:6379
QBRAID_CLIENT_ID=your-qbraid-client-id
QBRAID_CLIENT_SECRET=your-qbraid-client-secret
COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx
COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
COGNITO_CLIENT_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxx
```

This architecture provides a secure, scalable solution for managing authentication and API access while keeping sensitive tokens away from the client-side code.
