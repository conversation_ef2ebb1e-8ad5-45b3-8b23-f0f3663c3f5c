# Refresh Token Management Solution

## Overview

This document explains how refresh tokens are obtained, stored, and used in the API gateway for authenticating with third-party APIs.

## The Problem

The original question was: **"Where are we getting the refresh token from?"**

The API gateway needs refresh tokens to authenticate with third-party APIs (like QBraid), but these tokens need to be:
1. Obtained securely from the authentication provider (AWS Cognito)
2. Stored securely on the backend
3. Retrieved automatically when making API calls

## The Solution

### 1. Token Acquisition Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as Auth Action
    participant C as AWS Cognito
    participant S as Token Storage API
    participant G as API Gateway

    U->>F: Login with email/password
    F->>A: authenticateUser()
    A->>C: signIn()
    C->>A: Return auth result + tokens
    A->>A: Create JWT session
    A->>C: fetchAuthSession()
    C->>A: Return Cognito tokens
    A->>S: Store tokens via /api/store-cognito-tokens
    S->>S: Store in memory/database
    A->>F: Return success
    
    Note over U,G: Later API Request
    F->>G: API request with session cookie
    G->>S: getStoredRefreshToken(userId)
    S->>G: Return stored refresh token
    G->>G: Add to API headers
```

### 2. Implementation Details

#### A. Token Extraction (app/auth/actions.ts)

```typescript
if (result.isSignedIn) {
  // Create JWT session
  const sessionToken = await createSession({ username: email, email });
  await setSessionCookie(sessionToken);

  // Store Cognito tokens for API authentication
  try {
    const authSession = await fetchAuthSession();
    const tokens = authSession.tokens;
    
    if (tokens?.accessToken) {
      await fetch('/api/store-cognito-tokens', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Cookie': `session=${sessionToken}`
        },
        body: JSON.stringify({
          cognitoRefreshToken: tokens.accessToken.toString(),
          cognitoAccessToken: tokens.accessToken.toString(),
          cognitoIdToken: tokens.idToken?.toString() || '',
        }),
      });
    }
  } catch (tokenError) {
    console.warn('Failed to store Cognito tokens:', tokenError);
  }
}
```

#### B. Token Storage (app/api/store-cognito-tokens/route.ts)

```typescript
// In-memory storage for development (replace with database/Redis in production)
const tokenStorage = new Map<string, {
  refreshToken: string;
  accessToken: string;
  idToken: string;
  storedAt: number;
}>();

export async function POST(request: NextRequest) {
  const user = await validateSession(request);
  if (!user) return createErrorResponse('Unauthorized', 401);

  const { cognitoRefreshToken, cognitoAccessToken, cognitoIdToken } = await request.json();
  
  const userId = user.userId || user.username;
  tokenStorage.set(userId, {
    refreshToken: cognitoRefreshToken,
    accessToken: cognitoAccessToken,
    idToken: cognitoIdToken,
    storedAt: Date.now(),
  });

  return Response.json({ success: true });
}
```

#### C. Token Retrieval (app/api/_utils/auth.ts)

```typescript
async function getStoredRefreshToken(userId: string): Promise<string | null> {
  try {
    const { getStoredRefreshTokenForUser } = await import('../store-cognito-tokens/route');
    return getStoredRefreshTokenForUser(userId);
  } catch (error) {
    console.warn('Could not retrieve stored refresh token:', error);
    return null;
  }
}

export async function getAuthHeaders(user: any): Promise<Record<string, string>> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add user email
  if (user?.email) {
    headers['email'] = user.email;
  }

  // Get stored refresh token
  if (user?.userId) {
    const storedRefreshToken = await getStoredRefreshToken(user.userId);
    if (storedRefreshToken) {
      headers['refresh-token'] = storedRefreshToken;
    }
  }

  // Development fallback
  if (process.env.NODE_ENV === 'development') {
    if (!headers['email'] && process.env.NEXT_PUBLIC_EMAIL) {
      headers['email'] = process.env.NEXT_PUBLIC_EMAIL;
    }
    if (!headers['refresh-token'] && process.env.NEXT_PUBLIC_REFRESH_TOKEN) {
      headers['refresh-token'] = process.env.NEXT_PUBLIC_REFRESH_TOKEN;
    }
  }

  return headers;
}
```

### 3. Token Sources Priority

The system uses the following priority for obtaining refresh tokens:

1. **Stored Cognito Tokens** (Production)
   - Extracted during user login
   - Stored securely on backend
   - Retrieved automatically for API calls

2. **Environment Variables** (Development)
   - Fallback for development/testing
   - Set in `.env.local`
   - Only used when no stored tokens exist

### 4. Storage Options

#### Current Implementation (Development)
- **In-Memory Storage**: Simple Map for development
- **Pros**: Easy to implement, no dependencies
- **Cons**: Lost on server restart, not scalable

#### Production Recommendations

##### Option A: Database Storage
```sql
CREATE TABLE user_tokens (
  user_id VARCHAR(255) PRIMARY KEY,
  refresh_token TEXT ENCRYPTED,
  access_token TEXT ENCRYPTED,
  id_token TEXT ENCRYPTED,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

##### Option B: Redis Cache
```typescript
// Store tokens
await redis.hset(`user_tokens:${userId}`, {
  refresh_token: encrypt(refreshToken),
  access_token: encrypt(accessToken),
  stored_at: Date.now()
});

// Set expiration
await redis.expire(`user_tokens:${userId}`, 86400 * 30); // 30 days
```

##### Option C: Encrypted File Storage
```typescript
const tokenFile = path.join(process.cwd(), 'data', 'tokens', `${userId}.json`);
const encryptedTokens = encrypt(JSON.stringify(tokens));
await fs.writeFile(tokenFile, encryptedTokens);
```

### 5. Security Considerations

#### Token Encryption
```typescript
import crypto from 'crypto';

const ENCRYPTION_KEY = process.env.TOKEN_ENCRYPTION_KEY; // 32 bytes key

function encryptToken(token: string): string {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher('aes-256-cbc', ENCRYPTION_KEY);
  let encrypted = cipher.update(token, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
}

function decryptToken(encryptedToken: string): string {
  const [ivHex, encrypted] = encryptedToken.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}
```

#### Access Control
- Tokens only accessible by authenticated users
- User can only access their own tokens
- Tokens encrypted at rest
- Automatic cleanup of expired tokens

### 6. Testing the Implementation

#### Check Token Storage
```bash
# Login and check if tokens are stored
curl -b "session=your-jwt-token" http://localhost:3000/api/store-cognito-tokens
```

#### Test API with Stored Tokens
```bash
# Make API request - should use stored tokens automatically
curl -b "session=your-jwt-token" http://localhost:3000/api/quantum-devices
```

#### Development Fallback
```bash
# Set environment variables for development
echo "NEXT_PUBLIC_EMAIL=<EMAIL>" >> .env.local
echo "NEXT_PUBLIC_REFRESH_TOKEN=dev-token-123" >> .env.local
```

### 7. Migration Path

#### Phase 1: Current Implementation ✅
- In-memory token storage
- Cognito token extraction
- Development fallback

#### Phase 2: Production Storage
- Implement database/Redis storage
- Add token encryption
- Add token rotation

#### Phase 3: Advanced Features
- Automatic token refresh
- Token revocation
- Multi-tenant token isolation

## Summary

The refresh token management system:

1. **Extracts** Cognito tokens during user authentication
2. **Stores** them securely on the backend
3. **Retrieves** them automatically for API calls
4. **Falls back** to environment variables in development
5. **Provides** a clear migration path to production storage

This approach ensures that:
- No tokens are exposed to the client-side
- Authentication is handled automatically
- The system is secure and scalable
- Development workflow remains simple
