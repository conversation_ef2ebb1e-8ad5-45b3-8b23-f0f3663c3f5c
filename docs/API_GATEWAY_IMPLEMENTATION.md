# API Gateway Implementation

## Overview

This document describes the complete API gateway implementation using Next.js API routes that proxy requests to third-party APIs while handling authentication and session management.

## Architecture

```
Frontend (React) → Next.js API Routes → Third-Party APIs (QBraid)
                      ↑
                 Session Validation
                 Auth Headers
                 Request Logging
```

## Implemented API Routes

### 1. Authentication Utility (`app/api/_utils/auth.ts`)
- **Session Validation**: Validates JWT session tokens
- **Auth Headers**: Generates authentication headers for third-party APIs
- **Error Handling**: Standardized error responses
- **Environment Configuration**: Handles different API URLs per environment

### 2. Quantum Devices API

#### `GET /api/quantum-devices`
- **Purpose**: Fetches all quantum devices or a specific device
- **Query Parameters**: `qbraid_id` (optional)
- **Proxies to**: `/quantum-devices` or `/quantum-devices?qbraid_id={id}`

#### `PUT /api/quantum-devices/edit`
- **Purpose**: Updates quantum device data
- **Query Parameters**: `id` (required)
- **Body**: Device update data
- **Proxies to**: `/quantum-devices/edit?id={id}`

### 3. Quantum Jobs API

#### `GET /api/quantum-jobs/all-by-provider`
- **Purpose**: Fetches quantum jobs for a device and provider
- **Query Parameters**: 
  - `provider` (required)
  - `qbraidDeviceId` (required)
  - `page` (optional, default: 0)
  - `resultsPerPage` (optional, default: 10)
- **Proxies to**: `/quantum-jobs/all-by-provider`
- **Processing**: Parses timestamps and handles empty results

### 4. Organization API

#### `GET /api/orgs/get/[orgId]`
- **Purpose**: Fetches organization information
- **Parameters**: `orgId` (required)
- **Proxies to**: `/orgs/get/{orgId}`

#### `GET /api/orgs/users/[orgId]/[page]/[pageSize]`
- **Purpose**: Fetches paginated organization users
- **Parameters**: `orgId`, `page`, `pageSize` (all required)
- **Proxies to**: `/orgs/users/{orgId}/{page}/{pageSize}`

### 5. User Management API

#### `POST /api/orgs/users/add`
- **Purpose**: Invites new users to organization
- **Body**: User invitation data
- **Proxies to**: `/orgs/users/add`

#### `POST /api/orgs/users/update`
- **Purpose**: Updates user roles
- **Body**: User role update data
- **Proxies to**: `/orgs/users/update`

#### `POST /api/orgs/users/remove`
- **Purpose**: Removes users from organization
- **Body**: User removal data
- **Proxies to**: `/orgs/users/remove`

### 6. User Details API

#### `GET /api/get-user-details`
- **Purpose**: Fetches user profile details
- **Query Parameters**: `email` (required)
- **Proxies to**: `/get-user-details?email={email}`

### 7. Audit Logs API

#### `GET /api/audit-logs/[provider]`
- **Purpose**: Fetches audit logs for a provider
- **Parameters**: `provider` (required)
- **Query Parameters**: `page`, `resultsPerPage`
- **Proxies to**: `/audit-logs/{provider}`
- **Processing**: Parses timestamps

#### `POST /api/audit-logs`
- **Purpose**: Submits new audit log entries
- **Body**: Audit log data
- **Proxies to**: `/audit-logs`

## Security Features

### 1. Session Validation
- All routes validate JWT session tokens
- Unauthorized requests return 401 status
- Session data extracted for user context

### 2. Authentication Headers
- User email automatically added to requests
- Development fallback to environment variables
- Refresh tokens handled securely

### 3. Request Logging
- All API requests logged for audit purposes
- User actions tracked with email and timestamp
- Error logging for debugging

### 4. Error Handling
- Standardized error responses
- Proper HTTP status codes
- Error details logged server-side

## Environment Configuration

### Frontend Environment Variables
```env
# Points to your Next.js API routes
NEXT_PUBLIC_GATEWAY_API_URL=http://localhost:3000/api

# Development fallback credentials
NEXT_PUBLIC_EMAIL=<EMAIL>
NEXT_PUBLIC_REFRESH_TOKEN=your-refresh-token
```

### Backend Environment Variables
```env
# Third-party API URL for server-side requests
THIRD_PARTY_API_URL=https://api.qbraid.com/api

# Session management
SESSION_SECRET=your-jwt-secret
```

## Usage Examples

### Frontend API Calls (No Changes Required)
```typescript
// These calls now go through your API gateway automatically
import { fetchAllDevices } from '@/api-calls/devices';
import { fetchJobsForDevice } from '@/api-calls/jobs';

// All existing code works without modification
const devices = await fetchAllDevices();
const jobs = await fetchJobsForDevice('provider', 'device');
```

### Direct API Route Usage
```typescript
// You can also call the API routes directly
const response = await fetch('/api/quantum-devices', {
  credentials: 'include', // Include session cookies
});
const devices = await response.json();
```

## Benefits Achieved

### 1. Security
- ✅ No client-side API credentials
- ✅ Session-based authentication
- ✅ Centralized auth header management
- ✅ Request logging and audit trails

### 2. Maintainability
- ✅ Single point for API configuration
- ✅ Consistent error handling
- ✅ Easy to add new endpoints
- ✅ Environment-specific configurations

### 3. Performance
- ✅ Server-side request processing
- ✅ Potential for response caching
- ✅ Request deduplication possibilities
- ✅ Rate limiting capabilities

### 4. Developer Experience
- ✅ No changes required to existing frontend code
- ✅ Automatic authentication handling
- ✅ Standardized API responses
- ✅ Clear error messages

## Testing

### 1. Test Session Validation
```bash
# Should return 401 without session
curl http://localhost:3000/api/quantum-devices

# Should work with valid session cookie
curl -b "session=your-jwt-token" http://localhost:3000/api/quantum-devices
```

### 2. Test API Proxying
```bash
# Test device fetching
curl -b "session=your-jwt-token" http://localhost:3000/api/quantum-devices

# Test device update
curl -X PUT -b "session=your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"field":"value"}' \
  http://localhost:3000/api/quantum-devices/edit?id=device123
```

## Next Steps

1. **Deploy and Test**: Deploy the API routes and test with real sessions
2. **Add Caching**: Implement response caching for frequently accessed data
3. **Add Rate Limiting**: Implement rate limiting per user/endpoint
4. **Add Monitoring**: Add detailed logging and monitoring
5. **Add Token Refresh**: Implement automatic token refresh logic

This implementation provides a secure, maintainable API gateway that handles all authentication concerns while maintaining compatibility with existing frontend code.
