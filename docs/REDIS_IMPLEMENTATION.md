# Redis Implementation for Token Storage

## Overview

This document describes the Redis implementation using ioredis for production-ready token storage, replacing the in-memory storage solution.

## Architecture

```mermaid
graph TB
    subgraph "Application Layer"
        A[Auth Actions]
        B[API Gateway]
        C[Token Storage API]
    end
    
    subgraph "Redis Layer"
        D[Redis Client]
        E[Token Storage Class]
        F[Connection Pool]
    end
    
    subgraph "Redis Server"
        G[Redis Instance]
        H[Token Data]
        I[Session Data]
        J[Cache Data]
    end
    
    A --> C
    B --> E
    C --> E
    E --> D
    D --> F
    F --> G
    G --> H
    G --> I
    G --> J
```

## Implementation Details

### 1. Redis Client Configuration (`lib/redis.ts`)

#### Connection Management
```typescript
const redis = new Redis(redisUrl, {
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  family: 4, // IPv4
  keepAlive: true,
});
```

#### Key Features
- **Singleton Pattern**: Single Redis connection per application instance
- **Auto-Reconnection**: Automatic reconnection on connection loss
- **Error Handling**: Comprehensive error logging and recovery
- **Connection Pooling**: Efficient connection management

### 2. Token Storage Class

#### Core Operations
```typescript
class TokenStorage {
  // Store user tokens with 30-day expiration
  async storeTokens(userId: string, tokens: StoredTokens): Promise<void>
  
  // Retrieve all tokens for a user
  async getTokens(userId: string): Promise<StoredTokens | null>
  
  // Get only refresh token (optimized for API calls)
  async getRefreshToken(userId: string): Promise<string | null>
  
  // Update specific token fields
  async updateTokens(userId: string, updates: Partial<StoredTokens>): Promise<void>
  
  // Delete user tokens
  async deleteTokens(userId: string): Promise<void>
  
  // Check if user has stored tokens
  async hasTokens(userId: string): Promise<boolean>
}
```

#### Data Structure
```typescript
interface StoredTokens {
  refreshToken: string;
  accessToken: string;
  idToken: string;
  email: string;
  storedAt: number;
  expiresAt?: number;
}
```

### 3. Redis Key Schema

#### Key Patterns
```typescript
const RedisKeys = {
  userTokens: (userId: string) => `user_tokens:${userId}`,
  userSession: (sessionId: string) => `session:${sessionId}`,
  revokedSession: (sessionId: string) => `revoked_session:${sessionId}`,
  rateLimitUser: (userId: string) => `rate_limit:user:${userId}`,
  rateLimitIP: (ip: string) => `rate_limit:ip:${ip}`,
  apiCache: (endpoint: string, userId: string) => `api_cache:${endpoint}:${userId}`,
};
```

#### Storage Format
```redis
# Hash storage for efficient partial updates
HSET user_tokens:user123 refreshToken "token_value"
HSET user_tokens:user123 accessToken "access_value"
HSET user_tokens:user123 idToken "id_value"
HSET user_tokens:user123 email "<EMAIL>"
HSET user_tokens:user123 storedAt "1703123456789"

# Set expiration (30 days)
EXPIRE user_tokens:user123 2592000
```

## Setup Instructions

### 1. Install Redis

#### Local Development (macOS)
```bash
# Using Homebrew
brew install redis
brew services start redis

# Verify installation
redis-cli ping
# Should return: PONG
```

#### Local Development (Ubuntu/Debian)
```bash
# Install Redis
sudo apt update
sudo apt install redis-server

# Start Redis service
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Verify installation
redis-cli ping
```

#### Docker (Cross-platform)
```bash
# Run Redis in Docker
docker run -d \
  --name redis-server \
  -p 6379:6379 \
  redis:7-alpine

# Verify connection
docker exec -it redis-server redis-cli ping
```

### 2. Environment Configuration

#### Development (.env.local)
```env
# Redis connection
REDIS_URL=redis://localhost:6379

# Optional Redis password
# REDIS_PASSWORD=your-password

# Optional database selection
# REDIS_DB=0
```

#### Production
```env
# Redis connection with authentication
REDIS_URL=redis://username:password@redis-host:6379/0

# Or Redis Cluster
REDIS_URL=redis://node1:6379,redis://node2:6379,redis://node3:6379

# Redis SSL (if required)
REDIS_URL=rediss://username:password@redis-host:6380/0
```

### 3. Health Monitoring

#### Health Check Endpoint
```bash
# Basic health check
curl http://localhost:3000/api/health/redis

# Advanced diagnostics
curl -X POST http://localhost:3000/api/health/redis
```

#### Response Example
```json
{
  "status": "healthy",
  "connected": true,
  "operationsWork": true,
  "stats": {
    "dbSize": 42,
    "uptime": "2024-01-15T10:30:00.000Z"
  },
  "info": {
    "server": {
      "redis_version": "7.0.0",
      "uptime_in_seconds": "3600"
    },
    "memory": {
      "used_memory": "1048576",
      "used_memory_human": "1.00M"
    }
  }
}
```

## Migration from In-Memory Storage

### 1. Automatic Migration
The new Redis implementation is backward compatible. Existing functionality will automatically use Redis when available, falling back to environment variables in development.

### 2. Data Migration (if needed)
```typescript
// If you had existing in-memory data to migrate
async function migrateToRedis() {
  // This would only be needed if you had persistent in-memory data
  // The current implementation starts fresh with Redis
  console.log('Redis implementation is ready - no migration needed');
}
```

### 3. Testing Migration
```bash
# 1. Start Redis
redis-server

# 2. Test token storage
curl -X POST http://localhost:3000/api/store-cognito-tokens \
  -H "Content-Type: application/json" \
  -b "session=your-jwt-token" \
  -d '{"cognitoRefreshToken":"test-token"}'

# 3. Verify storage
curl http://localhost:3000/api/store-cognito-tokens \
  -b "session=your-jwt-token"

# 4. Test API calls use stored tokens
curl http://localhost:3000/api/quantum-devices \
  -b "session=your-jwt-token"
```

## Production Considerations

### 1. Redis Configuration

#### Memory Optimization
```redis
# redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

#### Security
```redis
# redis.conf
requirepass your-strong-password
bind 127.0.0.1
protected-mode yes
```

### 2. Monitoring

#### Key Metrics to Monitor
- **Memory Usage**: `used_memory`, `used_memory_peak`
- **Connections**: `connected_clients`, `rejected_connections`
- **Operations**: `total_commands_processed`, `instantaneous_ops_per_sec`
- **Persistence**: `last_save_time`, `changes_since_last_save`

#### Monitoring Tools
- Redis CLI: `redis-cli --stat`
- Redis Insight: GUI for Redis monitoring
- Prometheus + Grafana: For production monitoring

### 3. Backup and Recovery

#### Backup Strategy
```bash
# Manual backup
redis-cli BGSAVE

# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
redis-cli BGSAVE
cp /var/lib/redis/dump.rdb /backup/redis_backup_$DATE.rdb
```

#### Recovery
```bash
# Stop Redis
sudo systemctl stop redis

# Replace dump.rdb
sudo cp /backup/redis_backup_20240115.rdb /var/lib/redis/dump.rdb

# Start Redis
sudo systemctl start redis
```

## Performance Benefits

### 1. Scalability
- **Horizontal Scaling**: Redis Cluster support
- **Memory Efficiency**: Hash storage for token data
- **Connection Pooling**: Efficient connection management

### 2. Reliability
- **Persistence**: Data survives server restarts
- **Replication**: Master-slave setup for high availability
- **Automatic Failover**: Redis Sentinel support

### 3. Speed
- **In-Memory Storage**: Sub-millisecond response times
- **Optimized Operations**: Hash operations for token management
- **Connection Reuse**: Persistent connections reduce overhead

## Troubleshooting

### Common Issues

#### Connection Refused
```bash
# Check if Redis is running
redis-cli ping

# Check Redis logs
tail -f /var/log/redis/redis-server.log

# Restart Redis
sudo systemctl restart redis
```

#### Memory Issues
```bash
# Check memory usage
redis-cli info memory

# Clear all data (development only)
redis-cli FLUSHALL

# Set memory limit
redis-cli CONFIG SET maxmemory 1gb
```

#### Authentication Errors
```bash
# Test authentication
redis-cli -a your-password ping

# Check auth configuration
redis-cli CONFIG GET requirepass
```

This Redis implementation provides a production-ready, scalable solution for token storage with comprehensive monitoring and management capabilities.
